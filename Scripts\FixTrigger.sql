CREATE TRIGGER [dbo].[TR_ChartOfAccounts_Audit]
ON [dbo].[ChartOfAccounts]
AFTER INSERT, UPDATE, DELETE
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @Operation NVARCHAR(20);
    DECLARE @UserId NVARCHAR(100) = SYSTEM_USER;

    -- تحديد نوع العملية
    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
        SET @Operation = 'UPDATE';
    ELSE IF EXISTS (SELECT * FROM inserted)
        SET @Operation = 'INSERT';
    ELSE
        SET @Operation = 'DELETE';

    -- تسجيل عمليات الإدراج
    IF @Operation = 'INSERT'
    BEGIN
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, NewValue, ChangedBy)
        SELECT
            AccountId,
            'INSERT',
            'NEW_RECORD',
            'AccountCode: ' + AccountCode + ', AccountNameAr: ' + AccountNameAr + ', AccountNameEn: ' + ISNULL(AccountNameEn, ''),    
            @UserId
        FROM inserted;
    END

    -- تسجيل عمليات الحذف
    IF @Operation = 'DELETE'
    BEGIN
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, OldValue, ChangedBy)
        SELECT
            AccountId,
            'DELETE',
            'DELETED_RECORD',
            'AccountCode: ' + AccountCode + ', AccountNameAr: ' + AccountNameAr + ', AccountNameEn: ' + ISNULL(AccountNameEn, ''),
            @UserId
        FROM deleted;
    END

    -- تسجيل عمليات التعديل
    IF @Operation = 'UPDATE'
    BEGIN
        -- تسجيل تغيير رمز الحساب       
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, OldValue, NewValue, ChangedBy)
        SELECT
            i.AccountId,
            'UPDATE',
            'AccountCode',
            d.AccountCode,
            i.AccountCode,
            @UserId
        FROM inserted i
        INNER JOIN deleted d ON i.AccountId = d.AccountId  
        WHERE i.AccountCode != d.AccountCode;

        -- تسجيل تغيير الاسم الإنجليزي       
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, OldValue, NewValue, ChangedBy)
        SELECT
            i.AccountId,
            'UPDATE',
            'AccountNameEn',
            ISNULL(d.AccountNameEn, ''),
            ISNULL(i.AccountNameEn, ''),
            @UserId
        FROM inserted i
        INNER JOIN deleted d ON i.AccountId = d.AccountId
        WHERE ISNULL(i.AccountNameEn, '') != ISNULL(d.AccountNameEn, '');

        -- تسجيل تغيير الاسم العربي   
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, OldValue, NewValue, ChangedBy)
        SELECT
            i.AccountId,
            'UPDATE',
            'AccountNameAr',
            d.AccountNameAr,
            i.AccountNameAr,
            @UserId
        FROM inserted i
        INNER JOIN deleted d ON i.AccountId = d.AccountId  
        WHERE i.AccountNameAr != d.AccountNameAr;

        -- تسجيل تغيير نوع الحساب       
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, OldValue, NewValue, ChangedBy)
        SELECT
            i.AccountId,
            'UPDATE',
            'AccountTypeId',
            CAST(d.AccountTypeId AS NVARCHAR(10)),
            CAST(i.AccountTypeId AS NVARCHAR(10)),
            @UserId
        FROM inserted i
        INNER JOIN deleted d ON i.AccountId = d.AccountId  
        WHERE i.AccountTypeId != d.AccountTypeId;

        -- تسجيل تغيير الحساب الأب     
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, OldValue, NewValue, ChangedBy)
        SELECT
            i.AccountId,
            'UPDATE',
            'ParentAccountId',
            ISNULL(CAST(d.ParentAccountId AS NVARCHAR(10)), 'NULL'),
            ISNULL(CAST(i.ParentAccountId AS NVARCHAR(10)), 'NULL'),
            @UserId
        FROM inserted i
        INNER JOIN deleted d ON i.AccountId = d.AccountId
        WHERE ISNULL(i.ParentAccountId, -1) != ISNULL(d.ParentAccountId, -1);

        -- تسجيل تغيير حالة النشاط     
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, OldValue, NewValue, ChangedBy)
        SELECT
            i.AccountId,
            'UPDATE',
            'IsActive',
            CASE WHEN d.IsActive = 1 THEN 'نشط' ELSE 'غير نشط' END,
            CASE WHEN i.IsActive = 1 THEN 'نشط' ELSE 'غير نشط' END,
            @UserId
        FROM inserted i
        INNER JOIN deleted d ON i.AccountId = d.AccountId  
        WHERE i.IsActive != d.IsActive;

        -- تسجيل تغيير إمكانية الترحيل
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, OldValue, NewValue, ChangedBy)
        SELECT
            i.AccountId,
            'UPDATE',
            'AllowPosting',
            CASE WHEN d.AllowPosting = 1 THEN 'مسموح' ELSE 'غير مسموح' END,
            CASE WHEN i.AllowPosting = 1 THEN 'مسموح' ELSE 'غير مسموح' END,
            @UserId
        FROM inserted i
        INNER JOIN deleted d ON i.AccountId = d.AccountId  
        WHERE i.AllowPosting != d.AllowPosting;

        -- تسجيل تغيير الرصيد الافتتاحي
        INSERT INTO AccountAuditLog (AccountId, Operation, FieldName, OldValue, NewValue, ChangedBy)
        SELECT
            i.AccountId,
            'UPDATE',
            'OpeningBalance',
            CAST(d.OpeningBalance AS NVARCHAR(20)),
            CAST(i.OpeningBalance AS NVARCHAR(20)),        
            @UserId
        FROM inserted i
        INNER JOIN deleted d ON i.AccountId = d.AccountId  
        WHERE i.OpeningBalance != d.OpeningBalance;        
    END
END
