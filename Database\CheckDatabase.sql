-- ===================================================================
-- سكريبت فحص وإنشاء قاعدة البيانات
-- ===================================================================

-- التحقق من وجود قاعدة البيانات وإنشاؤها إذا لم تكن موجودة
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'AwqafManagement')
BEGIN
    CREATE DATABASE AwqafManagement;
    PRINT N'تم إنشاء قاعدة البيانات AwqafManagement';
END
ELSE
BEGIN
    PRINT N'قاعدة البيانات AwqafManagement موجودة بالفعل';
END

-- استخدام قاعدة البيانات
USE AwqafManagement;

-- التحقق من وجود الجداول الأساسية
PRINT N'';
PRINT N'=== فحص الجداول الأساسية ===';

-- فحص جدول المستخدمين
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND type in (N'U'))
    PRINT N'✓ جدول Users موجود';
ELSE
    PRINT N'✗ جدول Users غير موجود';

-- فحص جدول الأدوار
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Roles]') AND type in (N'U'))
    PRINT N'✓ جدول Roles موجود';
ELSE
    PRINT N'✗ جدول Roles غير موجود';

-- فحص جدول الصلاحيات
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Permissions]') AND type in (N'U'))
    PRINT N'✓ جدول Permissions موجود';
ELSE
    PRINT N'✗ جدول Permissions غير موجود';

-- فحص جداول المحاسبة
PRINT N'';
PRINT N'=== فحص جداول المحاسبة ===';

-- فحص جدول أنواع الحسابات
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[AccountTypes]') AND type in (N'U'))
    PRINT N'✓ جدول AccountTypes موجود';
ELSE
    PRINT N'✗ جدول AccountTypes غير موجود';

-- فحص جدول العملات
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Currencies]') AND type in (N'U'))
    PRINT N'✓ جدول Currencies موجود';
ELSE
    PRINT N'✗ جدول Currencies غير موجود';

-- فحص جدول الحسابات
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ChartOfAccounts]') AND type in (N'U'))
    PRINT N'✓ جدول ChartOfAccounts موجود';
ELSE
    PRINT N'✗ جدول ChartOfAccounts غير موجود';

-- فحص جدول البيانات الشخصية
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[AccountPersonalInfo]') AND type in (N'U'))
    PRINT N'✓ جدول AccountPersonalInfo موجود';
ELSE
    PRINT N'✗ جدول AccountPersonalInfo غير موجود';

-- فحص جدول سجل التعديلات
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[AccountAuditLog]') AND type in (N'U'))
    PRINT N'✓ جدول AccountAuditLog موجود';
ELSE
    PRINT N'✗ جدول AccountAuditLog غير موجود';

-- فحص البيانات الأساسية
PRINT N'';
PRINT N'=== فحص البيانات الأساسية ===';

-- فحص المستخدم الافتراضي
IF EXISTS (SELECT * FROM Users WHERE Username = 'admin')
    PRINT N'✓ المستخدم الافتراضي admin موجود';
ELSE
    PRINT N'✗ المستخدم الافتراضي admin غير موجود';

-- فحص أنواع الحسابات
IF EXISTS (SELECT * FROM AccountTypes)
BEGIN
    DECLARE @AccountTypesCount INT = (SELECT COUNT(*) FROM AccountTypes);
    PRINT N'✓ أنواع الحسابات موجودة (' + CAST(@AccountTypesCount AS NVARCHAR(10)) + N' نوع)';
END
ELSE
    PRINT N'✗ أنواع الحسابات غير موجودة';

-- فحص العملات
IF EXISTS (SELECT * FROM Currencies)
BEGIN
    DECLARE @CurrenciesCount INT = (SELECT COUNT(*) FROM Currencies);
    PRINT N'✓ العملات موجودة (' + CAST(@CurrenciesCount AS NVARCHAR(10)) + N' عملة)';
END
ELSE
    PRINT N'✗ العملات غير موجودة';

-- فحص الصلاحيات
IF EXISTS (SELECT * FROM Permissions WHERE PermissionName = 'ChartOfAccountsManagement')
    PRINT N'✓ صلاحيات الدليل المحاسبي موجودة';
ELSE
    PRINT N'✗ صلاحيات الدليل المحاسبي غير موجودة';

PRINT N'';
PRINT N'=== انتهى الفحص ===';
