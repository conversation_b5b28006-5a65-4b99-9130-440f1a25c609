using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Awqaf_Managment.Models.Accounting
{
    /// <summary>
    /// نموذج بيانات الحساب المحاسبي
    /// </summary>
    public class Account
    {
        public int AccountId { get; set; }
        
        [Required(ErrorMessage = "رمز الحساب مطلوب")]
        [StringLength(20, ErrorMessage = "رمز الحساب يجب ألا يزيد عن 20 حرف")]
        public string AccountCode { get; set; }
        
        [Required(ErrorMessage = "اسم الحساب باللغة العربية مطلوب")]
        [StringLength(200, ErrorMessage = "اسم الحساب يجب ألا يزيد عن 200 حرف")]
        public string AccountNameAr { get; set; }
        
        [StringLength(200, ErrorMessage = "اسم الحساب بالإنجليزية يجب ألا يزيد عن 200 حرف")]
        public string AccountNameEn { get; set; }
        
        [Required(ErrorMessage = "نوع الحساب مطلوب")]
        public int AccountTypeId { get; set; }
        
        public int? ParentAccountId { get; set; }
        
        public int Level { get; set; }
        
        [Required(ErrorMessage = "العملة مطلوبة")]
        public int CurrencyId { get; set; }
        
        public decimal OpeningBalance { get; set; }
        
        public decimal CurrentBalance { get; set; }
        
        public bool IsActive { get; set; }
        
        public bool AllowTransactions { get; set; }
        
        [StringLength(500, ErrorMessage = "الوصف يجب ألا يزيد عن 500 حرف")]
        public string Description { get; set; }
        
        public DateTime CreatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public int? ModifiedBy { get; set; }

        // خصائص التنقل (Navigation Properties)
        public AccountType AccountType { get; set; }
        public Account ParentAccount { get; set; }
        public Currency Currency { get; set; }
        public List<Account> ChildAccounts { get; set; }
        public AccountPersonalInfo PersonalInfo { get; set; }

        public Account()
        {
            Level = 1;
            OpeningBalance = 0.0m;
            CurrentBalance = 0.0m;
            IsActive = true;
            AllowTransactions = true;
            CreatedDate = DateTime.Now;
            ChildAccounts = new List<Account>();
        }

        /// <summary>
        /// عرض الحساب للواجهة
        /// </summary>
        public string DisplayName => $"{AccountCode} - {AccountNameAr}";

        /// <summary>
        /// عرض الحساب مع المستوى
        /// </summary>
        public string DisplayWithLevel => $"{new string(' ', (Level - 1) * 4)}{AccountCode} - {AccountNameAr}";

        /// <summary>
        /// عرض الرصيد مع العملة
        /// </summary>
        public string FormattedCurrentBalance => Currency != null ? 
            $"{CurrentBalance:N2} {Currency.Symbol}" : 
            $"{CurrentBalance:N2}";

        /// <summary>
        /// عرض الرصيد الافتتاحي مع العملة
        /// </summary>
        public string FormattedOpeningBalance => Currency != null ? 
            $"{OpeningBalance:N2} {Currency.Symbol}" : 
            $"{OpeningBalance:N2}";

        /// <summary>
        /// التحقق من كون الحساب رئيسي
        /// </summary>
        public bool IsMainAccount => ParentAccountId == null;

        /// <summary>
        /// التحقق من وجود حسابات فرعية
        /// </summary>
        public bool HasChildAccounts => ChildAccounts != null && ChildAccounts.Count > 0;

        /// <summary>
        /// حالة الحساب للعرض
        /// </summary>
        public string StatusDisplay => IsActive ? "نشط" : "غير نشط";

        /// <summary>
        /// حالة السماح بالحركات للعرض
        /// </summary>
        public string TransactionStatusDisplay => AllowTransactions ? "مسموح" : "غير مسموح";

        /// <summary>
        /// المسار الهرمي للحساب
        /// </summary>
        public string HierarchyPath { get; set; }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(AccountCode) && 
                   !string.IsNullOrWhiteSpace(AccountNameAr) &&
                   AccountTypeId > 0 &&
                   CurrencyId > 0;
        }

        /// <summary>
        /// التحقق من إمكانية الحذف
        /// </summary>
        public bool CanDelete()
        {
            return !HasChildAccounts && CurrentBalance == 0;
        }

        /// <summary>
        /// نسخ البيانات من حساب آخر
        /// </summary>
        public void CopyFrom(Account source)
        {
            if (source == null) return;

            AccountCode = source.AccountCode;
            AccountNameAr = source.AccountNameAr;
            AccountNameEn = source.AccountNameEn;
            AccountTypeId = source.AccountTypeId;
            ParentAccountId = source.ParentAccountId;
            CurrencyId = source.CurrencyId;
            OpeningBalance = source.OpeningBalance;
            IsActive = source.IsActive;
            AllowTransactions = source.AllowTransactions;
            Description = source.Description;
        }
    }
}
