using System;
using System.Configuration;
using System.Data.SqlClient;

namespace Awqaf_Managment.DataAccess
{
    /// <summary>
    /// فئة إدارة الاتصال بقاعدة البيانات
    /// </summary>
    public static class DatabaseConnection
    {
        private static string _connectionString;

        /// <summary>
        /// سلسلة الاتصال بقاعدة البيانات
        /// </summary>
        public static string ConnectionString
        {
            get
            {
                if (string.IsNullOrEmpty(_connectionString))
                {
                    _connectionString = GetConnectionString();
                }
                return _connectionString;
            }
        }

        /// <summary>
        /// الحصول على سلسلة الاتصال من ملف التكوين أو إنشاؤها افتراضياً
        /// </summary>
        private static string GetConnectionString()
        {
            try
            {
                // محاولة قراءة سلسلة الاتصال من App.config
                string connectionString = ConfigurationManager.ConnectionStrings["AwqafManagement"]?.ConnectionString;
                
                if (!string.IsNullOrEmpty(connectionString))
                {
                    return connectionString;
                }
            }
            catch (Exception)
            {
                // في حالة عدم وجود ملف التكوين أو خطأ في القراءة
            }

            // سلسلة الاتصال الافتراضية لـ SQL Server 2014 مع دعم Unicode
            return @"Data Source=NAJEEB;Initial Catalog=AwqafManagement;User ID=محمد; Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False";
        }

        /// <summary>
        /// إنشاء اتصال جديد بقاعدة البيانات
        /// </summary>
        /// <returns>كائن SqlConnection</returns>
        public static SqlConnection CreateConnection()
        {
            return new SqlConnection(ConnectionString);
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        /// <returns>true إذا كان الاتصال ناجحاً، false إذا فشل</returns>
        public static bool TestConnection()
        {
            try
            {
                using (var connection = CreateConnection())
                {
                    connection.Open();
                    return true;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات مع إرجاع رسالة الخطأ
        /// </summary>
        /// <param name="errorMessage">رسالة الخطأ في حالة الفشل</param>
        /// <returns>true إذا كان الاتصال ناجحاً، false إذا فشل</returns>
        public static bool TestConnection(out string errorMessage)
        {
            errorMessage = string.Empty;
            
            try
            {
                using (var connection = CreateConnection())
                {
                    connection.Open();
                    return true;
                }
            }
            catch (SqlException sqlEx)
            {
                errorMessage = $"خطأ في قاعدة البيانات: {sqlEx.Message}";
                return false;
            }
            catch (Exception ex)
            {
                errorMessage = $"خطأ في الاتصال: {ex.Message}";
                return false;
            }
        }

        /// <summary>
        /// تحديث سلسلة الاتصال
        /// </summary>
        /// <param name="newConnectionString">سلسلة الاتصال الجديدة</param>
        public static void UpdateConnectionString(string newConnectionString)
        {
            _connectionString = newConnectionString;
        }
    }
}
