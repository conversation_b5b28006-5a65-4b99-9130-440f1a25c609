using System;

namespace Awqaf_Managment.Models.Accounting
{
    /// <summary>
    /// نموذج بيانات نوع الحساب
    /// </summary>
    public class AccountType
    {
        public int AccountTypeId { get; set; }
        public string TypeName { get; set; }
        public string TypeNameAr { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public int? ModifiedBy { get; set; }

        public AccountType()
        {
            IsActive = true;
            CreatedDate = DateTime.Now;
        }

        /// <summary>
        /// عرض النوع للواجهة
        /// </summary>
        public string DisplayName => TypeNameAr;

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(TypeName) && 
                   !string.IsNullOrWhiteSpace(TypeNameAr);
        }
    }
}
