using System;

namespace Awqaf_Managment.Models.Accounting
{
    /// <summary>
    /// نموذج سجل تعديلات الحسابات
    /// </summary>
    public class AccountAuditLog
    {
        public int AuditId { get; set; }
        public int AccountId { get; set; }
        public string Action { get; set; }
        public string FieldName { get; set; }
        public string OldValue { get; set; }
        public string NewValue { get; set; }
        public DateTime ActionDate { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; }
        public string IPAddress { get; set; }
        public string Description { get; set; }

        // خصائص التنقل
        public Account Account { get; set; }

        public AccountAuditLog()
        {
            ActionDate = DateTime.Now;
        }

        /// <summary>
        /// عرض نوع العملية
        /// </summary>
        public string ActionDisplay
        {
            get
            {
                return Action switch
                {
                    "INSERT" => "إضافة",
                    "UPDATE" => "تعديل",
                    "DELETE" => "حذف",
                    _ => Action
                };
            }
        }

        /// <summary>
        /// عرض تاريخ العملية
        /// </summary>
        public string ActionDateDisplay => ActionDate.ToString("dd/MM/yyyy HH:mm:ss");

        /// <summary>
        /// عرض تفاصيل التغيير
        /// </summary>
        public string ChangeDetails
        {
            get
            {
                if (!string.IsNullOrWhiteSpace(FieldName) && 
                    !string.IsNullOrWhiteSpace(OldValue) && 
                    !string.IsNullOrWhiteSpace(NewValue))
                {
                    return $"{GetFieldDisplayName(FieldName)}: من '{OldValue}' إلى '{NewValue}'";
                }
                else if (!string.IsNullOrWhiteSpace(Description))
                {
                    return Description;
                }
                else
                {
                    return ActionDisplay;
                }
            }
        }

        /// <summary>
        /// الحصول على اسم الحقل للعرض
        /// </summary>
        private string GetFieldDisplayName(string fieldName)
        {
            return fieldName switch
            {
                "AccountCode" => "رمز الحساب",
                "AccountNameAr" => "اسم الحساب (عربي)",
                "AccountNameEn" => "اسم الحساب (إنجليزي)",
                "AccountTypeId" => "نوع الحساب",
                "ParentAccountId" => "الحساب الأب",
                "CurrencyId" => "العملة",
                "OpeningBalance" => "الرصيد الافتتاحي",
                "CurrentBalance" => "الرصيد الحالي",
                "IsActive" => "حالة النشاط",
                "AllowTransactions" => "السماح بالحركات",
                "Description" => "الوصف",
                _ => fieldName
            };
        }

        /// <summary>
        /// عرض ملخص العملية
        /// </summary>
        public string Summary
        {
            get
            {
                var accountInfo = Account != null ? Account.DisplayName : $"الحساب #{AccountId}";
                return $"{ActionDisplay} {accountInfo} بواسطة {UserName} في {ActionDateDisplay}";
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        public bool IsValid()
        {
            return AccountId > 0 && 
                   !string.IsNullOrWhiteSpace(Action) &&
                   UserId > 0 &&
                   !string.IsNullOrWhiteSpace(UserName);
        }

        /// <summary>
        /// إنشاء سجل تعديل جديد
        /// </summary>
        public static AccountAuditLog CreateInsertLog(int accountId, int userId, string userName, string description = null)
        {
            return new AccountAuditLog
            {
                AccountId = accountId,
                Action = "INSERT",
                UserId = userId,
                UserName = userName,
                Description = description ?? "تم إنشاء حساب جديد",
                ActionDate = DateTime.Now
            };
        }

        /// <summary>
        /// إنشاء سجل تعديل للتحديث
        /// </summary>
        public static AccountAuditLog CreateUpdateLog(int accountId, string fieldName, string oldValue, string newValue, int userId, string userName)
        {
            return new AccountAuditLog
            {
                AccountId = accountId,
                Action = "UPDATE",
                FieldName = fieldName,
                OldValue = oldValue,
                NewValue = newValue,
                UserId = userId,
                UserName = userName,
                ActionDate = DateTime.Now
            };
        }

        /// <summary>
        /// إنشاء سجل تعديل للحذف
        /// </summary>
        public static AccountAuditLog CreateDeleteLog(int accountId, int userId, string userName, string description = null)
        {
            return new AccountAuditLog
            {
                AccountId = accountId,
                Action = "DELETE",
                UserId = userId,
                UserName = userName,
                Description = description ?? "تم حذف الحساب",
                ActionDate = DateTime.Now
            };
        }
    }

    /// <summary>
    /// أنواع العمليات في سجل التعديلات
    /// </summary>
    public static class AuditActions
    {
        public const string Insert = "INSERT";
        public const string Update = "UPDATE";
        public const string Delete = "DELETE";
    }
}
