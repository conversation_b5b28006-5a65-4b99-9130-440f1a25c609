-- ===================================================================
-- الإجراءات المخزنة للدليل المحاسبي
-- Stored Procedures for Chart of Accounts
-- ===================================================================

USE AwqafManagement;
GO

-- ===================================================================
-- إجراء البحث في الحسابات
-- ===================================================================
CREATE OR ALTER PROCEDURE [dbo].[SP_SearchAccounts]
    @SearchTerm NVARCHAR(200) = NULL,
    @AccountTypeId INT = NULL,
    @ParentAccountId INT = NULL,
    @IsActive BIT = NULL,
    @AllowTransactions BIT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        a.AccountId,
        a.AccountCode,
        a.AccountNameAr,
        a.AccountNameEn,
        a.AccountTypeId,
        at.TypeNameAr AS AccountTypeName,
        a.ParentAccountId,
        pa.AccountNameAr AS ParentAccountName,
        a.Level,
        a.CurrencyId,
        c.CurrencyNameAr AS CurrencyName,
        c.Symbol AS CurrencySymbol,
        a.OpeningBalance,
        a.CurrentBalance,
        a.IsActive,
        a.AllowTransactions,
        a.Description,
        a.CreatedDate,
        a.ModifiedDate
    FROM ChartOfAccounts a
    INNER JOIN AccountTypes at ON a.AccountTypeId = at.AccountTypeId
    LEFT JOIN ChartOfAccounts pa ON a.ParentAccountId = pa.AccountId
    INNER JOIN Currencies c ON a.CurrencyId = c.CurrencyId
    WHERE 
        (@SearchTerm IS NULL OR 
         a.AccountCode LIKE '%' + @SearchTerm + '%' OR
         a.AccountNameAr LIKE '%' + @SearchTerm + '%' OR
         a.AccountNameEn LIKE '%' + @SearchTerm + '%' OR
         a.Description LIKE '%' + @SearchTerm + '%')
    AND (@AccountTypeId IS NULL OR a.AccountTypeId = @AccountTypeId)
    AND (@ParentAccountId IS NULL OR a.ParentAccountId = @ParentAccountId)
    AND (@IsActive IS NULL OR a.IsActive = @IsActive)
    AND (@AllowTransactions IS NULL OR a.AllowTransactions = @AllowTransactions)
    ORDER BY a.AccountCode;
END
GO

-- ===================================================================
-- إجراء الحصول على الحسابات الهرمية
-- ===================================================================
CREATE OR ALTER PROCEDURE [dbo].[SP_GetAccountsHierarchy]
    @ParentAccountId INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    WITH AccountHierarchy AS (
        -- المستوى الأول (الحسابات الرئيسية)
        SELECT 
            a.AccountId,
            a.AccountCode,
            a.AccountNameAr,
            a.AccountNameEn,
            a.AccountTypeId,
            at.TypeNameAr AS AccountTypeName,
            a.ParentAccountId,
            a.Level,
            a.CurrencyId,
            c.CurrencyNameAr AS CurrencyName,
            c.Symbol AS CurrencySymbol,
            a.OpeningBalance,
            a.CurrentBalance,
            a.IsActive,
            a.AllowTransactions,
            a.Description,
            CAST(a.AccountCode AS NVARCHAR(MAX)) AS HierarchyPath,
            0 AS HierarchyLevel
        FROM ChartOfAccounts a
        INNER JOIN AccountTypes at ON a.AccountTypeId = at.AccountTypeId
        INNER JOIN Currencies c ON a.CurrencyId = c.CurrencyId
        WHERE a.ParentAccountId IS NULL OR a.ParentAccountId = ISNULL(@ParentAccountId, a.ParentAccountId)
        
        UNION ALL
        
        -- المستويات التابعة
        SELECT 
            a.AccountId,
            a.AccountCode,
            a.AccountNameAr,
            a.AccountNameEn,
            a.AccountTypeId,
            at.TypeNameAr AS AccountTypeName,
            a.ParentAccountId,
            a.Level,
            a.CurrencyId,
            c.CurrencyNameAr AS CurrencyName,
            c.Symbol AS CurrencySymbol,
            a.OpeningBalance,
            a.CurrentBalance,
            a.IsActive,
            a.AllowTransactions,
            a.Description,
            ah.HierarchyPath + ' > ' + a.AccountCode,
            ah.HierarchyLevel + 1
        FROM ChartOfAccounts a
        INNER JOIN AccountTypes at ON a.AccountTypeId = at.AccountTypeId
        INNER JOIN Currencies c ON a.CurrencyId = c.CurrencyId
        INNER JOIN AccountHierarchy ah ON a.ParentAccountId = ah.AccountId
    )
    SELECT * FROM AccountHierarchy
    ORDER BY HierarchyPath;
END
GO

-- ===================================================================
-- إجراء إضافة حساب جديد
-- ===================================================================
CREATE OR ALTER PROCEDURE [dbo].[SP_InsertAccount]
    @AccountCode NVARCHAR(20),
    @AccountNameAr NVARCHAR(200),
    @AccountNameEn NVARCHAR(200) = NULL,
    @AccountTypeId INT,
    @ParentAccountId INT = NULL,
    @CurrencyId INT,
    @OpeningBalance DECIMAL(18,4) = 0.0,
    @IsActive BIT = 1,
    @AllowTransactions BIT = 1,
    @Description NVARCHAR(500) = NULL,
    @CreatedBy INT,
    @NewAccountId INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- حساب المستوى
        DECLARE @Level INT = 1;
        IF @ParentAccountId IS NOT NULL
        BEGIN
            SELECT @Level = Level + 1 FROM ChartOfAccounts WHERE AccountId = @ParentAccountId;
        END
        
        -- إدراج الحساب الجديد
        INSERT INTO ChartOfAccounts (
            AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, 
            ParentAccountId, Level, CurrencyId, OpeningBalance, CurrentBalance,
            IsActive, AllowTransactions, Description, CreatedBy
        ) VALUES (
            @AccountCode, @AccountNameAr, @AccountNameEn, @AccountTypeId,
            @ParentAccountId, @Level, @CurrencyId, @OpeningBalance, @OpeningBalance,
            @IsActive, @AllowTransactions, @Description, @CreatedBy
        );
        
        SET @NewAccountId = SCOPE_IDENTITY();
        
        -- تسجيل في سجل التعديلات
        INSERT INTO AccountAuditLog (AccountId, Action, Description, UserId, UserName)
        SELECT @NewAccountId, 'INSERT', N'تم إنشاء حساب جديد: ' + @AccountNameAr, 
               @CreatedBy, u.FullName
        FROM Users u WHERE u.UserId = @CreatedBy;
        
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

-- ===================================================================
-- إجراء تحديث حساب
-- ===================================================================
CREATE OR ALTER PROCEDURE [dbo].[SP_UpdateAccount]
    @AccountId INT,
    @AccountCode NVARCHAR(20),
    @AccountNameAr NVARCHAR(200),
    @AccountNameEn NVARCHAR(200) = NULL,
    @AccountTypeId INT,
    @ParentAccountId INT = NULL,
    @CurrencyId INT,
    @OpeningBalance DECIMAL(18,4),
    @IsActive BIT,
    @AllowTransactions BIT,
    @Description NVARCHAR(500) = NULL,
    @ModifiedBy INT
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- التحقق من وجود الحساب
        IF NOT EXISTS (SELECT 1 FROM ChartOfAccounts WHERE AccountId = @AccountId)
        BEGIN
            RAISERROR(N'الحساب غير موجود', 16, 1);
            RETURN;
        END
        
        -- حساب المستوى الجديد
        DECLARE @Level INT = 1;
        IF @ParentAccountId IS NOT NULL
        BEGIN
            SELECT @Level = Level + 1 FROM ChartOfAccounts WHERE AccountId = @ParentAccountId;
        END
        
        -- حفظ القيم القديمة لسجل التعديلات
        DECLARE @OldValues TABLE (
            FieldName NVARCHAR(100),
            OldValue NVARCHAR(500),
            NewValue NVARCHAR(500)
        );
        
        INSERT INTO @OldValues (FieldName, OldValue, NewValue)
        SELECT 'AccountCode', AccountCode, @AccountCode FROM ChartOfAccounts WHERE AccountId = @AccountId AND AccountCode != @AccountCode
        UNION ALL
        SELECT 'AccountNameAr', AccountNameAr, @AccountNameAr FROM ChartOfAccounts WHERE AccountId = @AccountId AND AccountNameAr != @AccountNameAr
        UNION ALL
        SELECT 'AccountNameEn', ISNULL(AccountNameEn, ''), ISNULL(@AccountNameEn, '') FROM ChartOfAccounts WHERE AccountId = @AccountId AND ISNULL(AccountNameEn, '') != ISNULL(@AccountNameEn, '')
        UNION ALL
        SELECT 'OpeningBalance', CAST(OpeningBalance AS NVARCHAR), CAST(@OpeningBalance AS NVARCHAR) FROM ChartOfAccounts WHERE AccountId = @AccountId AND OpeningBalance != @OpeningBalance;
        
        -- تحديث الحساب
        UPDATE ChartOfAccounts SET
            AccountCode = @AccountCode,
            AccountNameAr = @AccountNameAr,
            AccountNameEn = @AccountNameEn,
            AccountTypeId = @AccountTypeId,
            ParentAccountId = @ParentAccountId,
            Level = @Level,
            CurrencyId = @CurrencyId,
            OpeningBalance = @OpeningBalance,
            IsActive = @IsActive,
            AllowTransactions = @AllowTransactions,
            Description = @Description,
            ModifiedDate = GETDATE(),
            ModifiedBy = @ModifiedBy
        WHERE AccountId = @AccountId;
        
        -- تسجيل التعديلات في سجل التعديلات
        INSERT INTO AccountAuditLog (AccountId, Action, FieldName, OldValue, NewValue, UserId, UserName)
        SELECT @AccountId, 'UPDATE', ov.FieldName, ov.OldValue, ov.NewValue, 
               @ModifiedBy, u.FullName
        FROM @OldValues ov
        CROSS JOIN Users u 
        WHERE u.UserId = @ModifiedBy;
        
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

-- ===================================================================
-- إجراء حذف حساب
-- ===================================================================
CREATE OR ALTER PROCEDURE [dbo].[SP_DeleteAccount]
    @AccountId INT,
    @DeletedBy INT
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- التحقق من وجود الحساب
        IF NOT EXISTS (SELECT 1 FROM ChartOfAccounts WHERE AccountId = @AccountId)
        BEGIN
            RAISERROR(N'الحساب غير موجود', 16, 1);
            RETURN;
        END
        
        -- التحقق من وجود حسابات فرعية
        IF EXISTS (SELECT 1 FROM ChartOfAccounts WHERE ParentAccountId = @AccountId)
        BEGIN
            RAISERROR(N'لا يمكن حذف الحساب لوجود حسابات فرعية تابعة له', 16, 1);
            RETURN;
        END
        
        -- حفظ بيانات الحساب للسجل
        DECLARE @AccountName NVARCHAR(200);
        SELECT @AccountName = AccountNameAr FROM ChartOfAccounts WHERE AccountId = @AccountId;
        
        -- حذف الحساب
        DELETE FROM ChartOfAccounts WHERE AccountId = @AccountId;
        
        -- تسجيل في سجل التعديلات
        INSERT INTO AccountAuditLog (AccountId, Action, Description, UserId, UserName)
        SELECT @AccountId, 'DELETE', N'تم حذف الحساب: ' + @AccountName, 
               @DeletedBy, u.FullName
        FROM Users u WHERE u.UserId = @DeletedBy;
        
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

PRINT N'تم إنشاء الإجراءات المخزنة للدليل المحاسبي بنجاح';
