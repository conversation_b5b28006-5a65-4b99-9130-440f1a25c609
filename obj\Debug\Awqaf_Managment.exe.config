﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>

    <connectionStrings>
        <!-- سلسلة الاتصال بقاعدة البيانات SQL Server 2014 -->
        <add name="AwqafManagement"
             connectionString="Data Source=NAJEEB;Database=AwqafManagement;  Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False"
             providerName="System.Data.SqlClient" />
    </connectionStrings>

    <appSettings>
        <!-- إعدادات التطبيق العامة -->
        <add key="ApplicationName" value="نظام إدارة الأوقاف" />
        <add key="ApplicationVersion" value="1.0.0" />
        <add key="CompanyName" value="إدارة الأوقاف" />

        <!-- إعدادات الأمان -->
        <add key="MaxLoginAttempts" value="5" />
        <add key="PasswordMinLength" value="6" />
        <add key="SessionTimeoutMinutes" value="30" />

        <!-- إعدادات قاعدة البيانات -->
        <add key="DatabaseBackupPath" value="C:\Backups\AwqafManagement" />
        <add key="CommandTimeout" value="30" />

        <!-- إعدادات التقارير -->
        <add key="ReportsPath" value="Reports" />
        <add key="TempPath" value="Temp" />

        <!-- إعدادات اللغة والعرض -->
        <add key="DefaultLanguage" value="ar-SA" />
        <add key="DateFormat" value="dd/MM/yyyy" />
        <add key="CurrencySymbol" value="ر.س" />
    </appSettings>
</configuration>