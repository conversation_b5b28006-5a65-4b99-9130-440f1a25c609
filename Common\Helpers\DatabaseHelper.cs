using System;
using System.Data.SqlClient;
using System.Windows.Forms;
using Awqaf_Managment.DataAccess;

namespace Awqaf_Managment.Common.Helpers
{
    /// <summary>
    /// مساعد فحص قاعدة البيانات
    /// </summary>
    public static class DatabaseHelper
    {
        /// <summary>
        /// فحص الاتصال بقاعدة البيانات
        /// </summary>
        public static bool TestConnection()
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    return true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في الاتصال بقاعدة البيانات:\n{ex.Message}",
                    "خطأ في الاتصال",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1,
                    MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
                return false;
            }
        }

        /// <summary>
        /// فحص وجود الجداول الأساسية
        /// </summary>
        public static bool CheckRequiredTables()
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();

                    string[] requiredTables = {
                        "Users", "Roles", "Permissions", "UserRoles", "RolePermissions",
                        "AccountTypes", "Currencies", "ChartOfAccounts", 
                        "AccountPersonalInfo", "AccountAuditLog"
                    };

                    foreach (string tableName in requiredTables)
                    {
                        string query = @"
                            SELECT COUNT(*) 
                            FROM INFORMATION_SCHEMA.TABLES 
                            WHERE TABLE_NAME = @TableName";

                        using (var command = new SqlCommand(query, connection))
                        {
                            command.Parameters.AddWithValue("@TableName", tableName);
                            int count = (int)command.ExecuteScalar();
                            
                            if (count == 0)
                            {
                                MessageBox.Show(
                                    $"الجدول المطلوب غير موجود: {tableName}\n\nيرجى تشغيل سكريبت إنشاء قاعدة البيانات أولاً.",
                                    "جدول مفقود",
                                    MessageBoxButtons.OK,
                                    MessageBoxIcon.Warning,
                                    MessageBoxDefaultButton.Button1,
                                    MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
                                return false;
                            }
                        }
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في فحص الجداول:\n{ex.Message}",
                    "خطأ في الفحص",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1,
                    MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
                return false;
            }
        }

        /// <summary>
        /// فحص وجود البيانات الأساسية
        /// </summary>
        public static bool CheckBasicData()
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();

                    // فحص المستخدم الافتراضي
                    string userQuery = "SELECT COUNT(*) FROM Users WHERE Username = 'admin'";
                    using (var command = new SqlCommand(userQuery, connection))
                    {
                        int userCount = (int)command.ExecuteScalar();
                        if (userCount == 0)
                        {
                            MessageBox.Show(
                                "المستخدم الافتراضي (admin) غير موجود.\n\nيرجى تشغيل سكريبت البيانات الأساسية.",
                                "بيانات مفقودة",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Warning,
                                MessageBoxDefaultButton.Button1,
                                MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
                            return false;
                        }
                    }

                    // فحص أنواع الحسابات
                    string accountTypesQuery = "SELECT COUNT(*) FROM AccountTypes";
                    using (var command = new SqlCommand(accountTypesQuery, connection))
                    {
                        int typesCount = (int)command.ExecuteScalar();
                        if (typesCount == 0)
                        {
                            MessageBox.Show(
                                "أنواع الحسابات غير موجودة.\n\nيرجى تشغيل سكريبت البيانات الأساسية.",
                                "بيانات مفقودة",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Warning,
                                MessageBoxDefaultButton.Button1,
                                MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
                            return false;
                        }
                    }

                    // فحص العملات
                    string currenciesQuery = "SELECT COUNT(*) FROM Currencies";
                    using (var command = new SqlCommand(currenciesQuery, connection))
                    {
                        int currenciesCount = (int)command.ExecuteScalar();
                        if (currenciesCount == 0)
                        {
                            MessageBox.Show(
                                "العملات غير موجودة.\n\nيرجى تشغيل سكريبت البيانات الأساسية.",
                                "بيانات مفقودة",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Warning,
                                MessageBoxDefaultButton.Button1,
                                MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
                            return false;
                        }
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في فحص البيانات الأساسية:\n{ex.Message}",
                    "خطأ في الفحص",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1,
                    MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
                return false;
            }
        }

        /// <summary>
        /// فحص شامل لقاعدة البيانات
        /// </summary>
        public static bool PerformFullCheck()
        {
            // فحص الاتصال
            if (!TestConnection())
            {
                return false;
            }

            // فحص الجداول
            if (!CheckRequiredTables())
            {
                return false;
            }

            // فحص البيانات الأساسية
            if (!CheckBasicData())
            {
                return false;
            }

            MessageBox.Show(
                "تم فحص قاعدة البيانات بنجاح!\n\nجميع الجداول والبيانات الأساسية موجودة.",
                "فحص ناجح",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1,
                MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);

            return true;
        }

        /// <summary>
        /// عرض معلومات قاعدة البيانات
        /// </summary>
        public static void ShowDatabaseInfo()
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();

                    string info = "معلومات قاعدة البيانات:\n\n";
                    info += $"الخادم: {connection.DataSource}\n";
                    info += $"قاعدة البيانات: {connection.Database}\n";
                    info += $"حالة الاتصال: {connection.State}\n\n";

                    // عدد الجداول
                    string tablesQuery = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'";
                    using (var command = new SqlCommand(tablesQuery, connection))
                    {
                        int tablesCount = (int)command.ExecuteScalar();
                        info += $"عدد الجداول: {tablesCount}\n";
                    }

                    // عدد المستخدمين
                    string usersQuery = "SELECT COUNT(*) FROM Users";
                    using (var command = new SqlCommand(usersQuery, connection))
                    {
                        int usersCount = (int)command.ExecuteScalar();
                        info += $"عدد المستخدمين: {usersCount}\n";
                    }

                    // عدد الحسابات
                    string accountsQuery = "SELECT COUNT(*) FROM ChartOfAccounts";
                    using (var command = new SqlCommand(accountsQuery, connection))
                    {
                        int accountsCount = (int)command.ExecuteScalar();
                        info += $"عدد الحسابات: {accountsCount}\n";
                    }

                    MessageBox.Show(
                        info,
                        "معلومات قاعدة البيانات",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information,
                        MessageBoxDefaultButton.Button1,
                        MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في الحصول على معلومات قاعدة البيانات:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1,
                    MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
            }
        }
    }
}
