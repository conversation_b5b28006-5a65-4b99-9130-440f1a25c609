using System;
using System.Windows.Forms;
using Awqaf_Managment.Common;
using Awqaf_Managment.Common.Helpers;
using Awqaf_Managment.Models.Accounting;
using Awqaf_Managment.Services.Accounting;
using Awqaf_Managment.Services.Security;

namespace Awqaf_Managment.UI.Forms.Accounting
{
    /// <summary>
    /// نموذج البيانات الشخصية للحساب
    /// </summary>
    public partial class AccountPersonalInfoForm : Form
    {
        private readonly int _accountId;
        private Account _account;
        private AccountPersonalInfo _personalInfo;

        public AccountPersonalInfoForm(int accountId)
        {
            InitializeComponent();
            _accountId = accountId;
            InitializeCustomComponents();
        }

        private void InitializeCustomComponents()
        {
            // تطبيق دعم RTL
            UIHelper.ApplyRTLSupport(this);
            
            // تطبيق الخط العربي
            UIHelper.ApplyArabicFont(this);
            
            // تخصيص الألوان
            this.BackColor = Constants.BackgroundColor;
            pnlHeader.BackColor = Constants.PrimaryColor;
            lblTitle.ForeColor = Constants.WhiteColor;
        }

        private void AccountPersonalInfoForm_Load(object sender, EventArgs e)
        {
            try
            {
                LoadAccountData();
                LoadPersonalInfo();
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تحميل البيانات: {ex.Message}");
                this.Close();
            }
        }

        private void LoadAccountData()
        {
            try
            {
                _account = AccountService.GetAccountById(_accountId);
                if (_account == null)
                {
                    throw new Exception("الحساب غير موجود");
                }

                // عرض معلومات الحساب
                lblAccountCodeValue.Text = _account.AccountCode;
                lblAccountNameValue.Text = _account.AccountNameAr;
                lblAccountTypeValue.Text = _account.AccountType?.TypeNameAr ?? "-";
                lblCurrencyValue.Text = _account.Currency?.DisplayName ?? "-";
                lblOpeningBalanceValue.Text = _account.FormattedOpeningBalance;
                lblCurrentBalanceValue.Text = _account.FormattedCurrentBalance;

                // تلوين الرصيد حسب القيمة
                if (_account.CurrentBalance > 0)
                    lblCurrentBalanceValue.ForeColor = Constants.SuccessColor;
                else if (_account.CurrentBalance < 0)
                    lblCurrentBalanceValue.ForeColor = Constants.ErrorColor;
                else
                    lblCurrentBalanceValue.ForeColor = Constants.TextColor;

                // تحديث العنوان
                lblTitle.Text = $"البيانات الشخصية للحساب: {_account.DisplayName}";
                this.Text = $"البيانات الشخصية - {_account.DisplayName}";
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحميل بيانات الحساب: {ex.Message}", ex);
            }
        }

        private void LoadPersonalInfo()
        {
            try
            {
                _personalInfo = AccountService.GetAccountPersonalInfo(_accountId);
                
                if (_personalInfo != null)
                {
                    // تعبئة الحقول
                    txtContactPersonName.Text = _personalInfo.ContactPersonName ?? "";
                    txtEmail.Text = _personalInfo.Email ?? "";
                    txtPhone.Text = _personalInfo.Phone ?? "";
                    txtMobile.Text = _personalInfo.Mobile ?? "";
                    txtAddress.Text = _personalInfo.Address ?? "";
                    txtCity.Text = _personalInfo.City ?? "";
                    txtCountry.Text = _personalInfo.Country ?? "";
                    txtPostalCode.Text = _personalInfo.PostalCode ?? "";
                    txtIdentityNumber.Text = _personalInfo.IdentityNumber ?? "";
                    txtTaxNumber.Text = _personalInfo.TaxNumber ?? "";
                    txtNotes.Text = _personalInfo.Notes ?? "";
                }
                else
                {
                    // إنشاء كائن جديد للبيانات الشخصية
                    _personalInfo = new AccountPersonalInfo
                    {
                        AccountId = _accountId
                    };
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحميل البيانات الشخصية: {ex.Message}", ex);
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                UpdatePersonalInfoFromInput();
                
                AccountService.SaveAccountPersonalInfo(_personalInfo, AuthenticationService.CurrentUser.UserId);
                
                UIHelper.ShowSuccess("تم حفظ البيانات الشخصية بنجاح");
                this.DialogResult = DialogResult.OK;
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في حفظ البيانات الشخصية: {ex.Message}");
            }
        }

        private bool ValidateInput()
        {
            errorProvider.Clear();
            bool isValid = true;

            // التحقق من البريد الإلكتروني
            if (!string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                try
                {
                    var addr = new System.Net.Mail.MailAddress(txtEmail.Text);
                    if (addr.Address != txtEmail.Text)
                    {
                        errorProvider.SetError(txtEmail, "البريد الإلكتروني غير صحيح");
                        isValid = false;
                    }
                }
                catch
                {
                    errorProvider.SetError(txtEmail, "البريد الإلكتروني غير صحيح");
                    isValid = false;
                }
            }

            // التحقق من رقم الهاتف
            if (!string.IsNullOrWhiteSpace(txtPhone.Text))
            {
                if (!System.Text.RegularExpressions.Regex.IsMatch(txtPhone.Text, @"^[\d\s\-\+\(\)]+$"))
                {
                    errorProvider.SetError(txtPhone, "رقم الهاتف غير صحيح");
                    isValid = false;
                }
            }

            // التحقق من رقم الجوال
            if (!string.IsNullOrWhiteSpace(txtMobile.Text))
            {
                if (!System.Text.RegularExpressions.Regex.IsMatch(txtMobile.Text, @"^[\d\s\-\+\(\)]+$"))
                {
                    errorProvider.SetError(txtMobile, "رقم الجوال غير صحيح");
                    isValid = false;
                }
            }

            return isValid;
        }

        private void UpdatePersonalInfoFromInput()
        {
            _personalInfo.ContactPersonName = string.IsNullOrWhiteSpace(txtContactPersonName.Text) ? null : txtContactPersonName.Text.Trim();
            _personalInfo.Email = string.IsNullOrWhiteSpace(txtEmail.Text) ? null : txtEmail.Text.Trim();
            _personalInfo.Phone = string.IsNullOrWhiteSpace(txtPhone.Text) ? null : txtPhone.Text.Trim();
            _personalInfo.Mobile = string.IsNullOrWhiteSpace(txtMobile.Text) ? null : txtMobile.Text.Trim();
            _personalInfo.Address = string.IsNullOrWhiteSpace(txtAddress.Text) ? null : txtAddress.Text.Trim();
            _personalInfo.City = string.IsNullOrWhiteSpace(txtCity.Text) ? null : txtCity.Text.Trim();
            _personalInfo.Country = string.IsNullOrWhiteSpace(txtCountry.Text) ? null : txtCountry.Text.Trim();
            _personalInfo.PostalCode = string.IsNullOrWhiteSpace(txtPostalCode.Text) ? null : txtPostalCode.Text.Trim();
            _personalInfo.IdentityNumber = string.IsNullOrWhiteSpace(txtIdentityNumber.Text) ? null : txtIdentityNumber.Text.Trim();
            _personalInfo.TaxNumber = string.IsNullOrWhiteSpace(txtTaxNumber.Text) ? null : txtTaxNumber.Text.Trim();
            _personalInfo.Notes = string.IsNullOrWhiteSpace(txtNotes.Text) ? null : txtNotes.Text.Trim();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// التحقق من وجود تغييرات غير محفوظة
        /// </summary>
        private bool HasUnsavedChanges()
        {
            if (_personalInfo == null) return false;

            return _personalInfo.ContactPersonName != (string.IsNullOrWhiteSpace(txtContactPersonName.Text) ? null : txtContactPersonName.Text.Trim()) ||
                   _personalInfo.Email != (string.IsNullOrWhiteSpace(txtEmail.Text) ? null : txtEmail.Text.Trim()) ||
                   _personalInfo.Phone != (string.IsNullOrWhiteSpace(txtPhone.Text) ? null : txtPhone.Text.Trim()) ||
                   _personalInfo.Mobile != (string.IsNullOrWhiteSpace(txtMobile.Text) ? null : txtMobile.Text.Trim()) ||
                   _personalInfo.Address != (string.IsNullOrWhiteSpace(txtAddress.Text) ? null : txtAddress.Text.Trim()) ||
                   _personalInfo.City != (string.IsNullOrWhiteSpace(txtCity.Text) ? null : txtCity.Text.Trim()) ||
                   _personalInfo.Country != (string.IsNullOrWhiteSpace(txtCountry.Text) ? null : txtCountry.Text.Trim()) ||
                   _personalInfo.PostalCode != (string.IsNullOrWhiteSpace(txtPostalCode.Text) ? null : txtPostalCode.Text.Trim()) ||
                   _personalInfo.IdentityNumber != (string.IsNullOrWhiteSpace(txtIdentityNumber.Text) ? null : txtIdentityNumber.Text.Trim()) ||
                   _personalInfo.TaxNumber != (string.IsNullOrWhiteSpace(txtTaxNumber.Text) ? null : txtTaxNumber.Text.Trim()) ||
                   _personalInfo.Notes != (string.IsNullOrWhiteSpace(txtNotes.Text) ? null : txtNotes.Text.Trim());
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (HasUnsavedChanges())
            {
                var result = MessageBox.Show(
                    "هناك تغييرات غير محفوظة. هل تريد حفظها قبل الإغلاق؟",
                    "تغييرات غير محفوظة",
                    MessageBoxButtons.YesNoCancel,
                    MessageBoxIcon.Question,
                    MessageBoxDefaultButton.Button1,
                    MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);

                if (result == DialogResult.Yes)
                {
                    btnSave_Click(this, EventArgs.Empty);
                    if (this.DialogResult != DialogResult.OK)
                    {
                        e.Cancel = true;
                        return;
                    }
                }
                else if (result == DialogResult.Cancel)
                {
                    e.Cancel = true;
                    return;
                }
            }

            base.OnFormClosing(e);
        }
    }
}
