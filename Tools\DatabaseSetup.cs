using System;
using System.IO;
using System.Data.SqlClient;
using System.Windows.Forms;
using Awqaf_Managment.DataAccess;

namespace Awqaf_Managment.Tools
{
    /// <summary>
    /// أداة إعداد قاعدة البيانات
    /// </summary>
    public static class DatabaseSetup
    {
        /// <summary>
        /// تشغيل سكريبت SQL من ملف
        /// </summary>
        public static bool ExecuteSqlScript(string scriptPath)
        {
            try
            {
                if (!File.Exists(scriptPath))
                {
                    MessageBox.Show(
                        $"ملف السكريبت غير موجود:\n{scriptPath}",
                        "ملف غير موجود",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error,
                        MessageBoxDefaultButton.Button1,
                        MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
                    return false;
                }

                string script = File.ReadAllText(scriptPath);
                return ExecuteSqlScript(script, Path.GetFileName(scriptPath));
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في قراءة ملف السكريبت:\n{ex.Message}",
                    "خطأ في القراءة",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1,
                    MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
                return false;
            }
        }

        /// <summary>
        /// تشغيل سكريبت SQL
        /// </summary>
        public static bool ExecuteSqlScript(string script, string scriptName = "SQL Script")
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();

                    // تقسيم السكريبت إلى أوامر منفصلة
                    string[] commands = script.Split(new string[] { "GO", "go" }, 
                        StringSplitOptions.RemoveEmptyEntries);

                    foreach (string commandText in commands)
                    {
                        string cleanCommand = commandText.Trim();
                        if (string.IsNullOrEmpty(cleanCommand))
                            continue;

                        using (var command = new SqlCommand(cleanCommand, connection))
                        {
                            command.CommandTimeout = 300; // 5 دقائق
                            command.ExecuteNonQuery();
                        }
                    }

                    MessageBox.Show(
                        $"تم تشغيل السكريبت بنجاح:\n{scriptName}",
                        "نجح التشغيل",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information,
                        MessageBoxDefaultButton.Button1,
                        MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);

                    return true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في تشغيل السكريبت {scriptName}:\n{ex.Message}",
                    "خطأ في التشغيل",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1,
                    MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
                return false;
            }
        }

        /// <summary>
        /// إعداد قاعدة البيانات الكاملة
        /// </summary>
        public static bool SetupDatabase()
        {
            try
            {
                string databasePath = Path.Combine(Application.StartupPath, "Database");
                
                // تشغيل سكريبت إنشاء قاعدة البيانات
                string createDbScript = Path.Combine(databasePath, "CreateDatabase.sql");
                if (File.Exists(createDbScript))
                {
                    if (!ExecuteSqlScript(createDbScript))
                        return false;
                }

                // تشغيل سكريبت البيانات الأساسية
                string basicDataScript = Path.Combine(databasePath, "BasicData.sql");
                if (File.Exists(basicDataScript))
                {
                    if (!ExecuteSqlScript(basicDataScript))
                        return false;
                }

                MessageBox.Show(
                    "تم إعداد قاعدة البيانات بنجاح!\n\nيمكنك الآن استخدام النظام.",
                    "إعداد ناجح",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information,
                    MessageBoxDefaultButton.Button1,
                    MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في إعداد قاعدة البيانات:\n{ex.Message}",
                    "خطأ في الإعداد",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1,
                    MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
                return false;
            }
        }

        /// <summary>
        /// فحص وإعداد قاعدة البيانات إذا لزم الأمر
        /// </summary>
        public static bool CheckAndSetupDatabase()
        {
            try
            {
                // محاولة الاتصال بقاعدة البيانات
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    
                    // فحص وجود الجداول الأساسية
                    string checkQuery = @"
                        SELECT COUNT(*) 
                        FROM INFORMATION_SCHEMA.TABLES 
                        WHERE TABLE_NAME IN ('Users', 'AccountTypes', 'ChartOfAccounts')";
                    
                    using (var command = new SqlCommand(checkQuery, connection))
                    {
                        int tablesCount = (int)command.ExecuteScalar();
                        
                        if (tablesCount < 3)
                        {
                            // الجداول غير موجودة، نحتاج لإعداد قاعدة البيانات
                            DialogResult result = MessageBox.Show(
                                "قاعدة البيانات غير مكتملة أو غير موجودة.\n\nهل تريد إعداد قاعدة البيانات الآن؟",
                                "إعداد قاعدة البيانات",
                                MessageBoxButtons.YesNo,
                                MessageBoxIcon.Question,
                                MessageBoxDefaultButton.Button1,
                                MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);

                            if (result == DialogResult.Yes)
                            {
                                return SetupDatabase();
                            }
                            else
                            {
                                return false;
                            }
                        }
                    }
                }

                return true;
            }
            catch (SqlException ex)
            {
                // قاعدة البيانات غير موجودة
                if (ex.Number == 4060) // Database does not exist
                {
                    DialogResult result = MessageBox.Show(
                        "قاعدة البيانات غير موجودة.\n\nهل تريد إنشاء قاعدة البيانات الآن؟",
                        "قاعدة البيانات غير موجودة",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question,
                        MessageBoxDefaultButton.Button1,
                        MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);

                    if (result == DialogResult.Yes)
                    {
                        return SetupDatabase();
                    }
                }
                else
                {
                    MessageBox.Show(
                        $"خطأ في الاتصال بقاعدة البيانات:\n{ex.Message}",
                        "خطأ في الاتصال",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error,
                        MessageBoxDefaultButton.Button1,
                        MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
                }

                return false;
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ غير متوقع:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1,
                    MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
                return false;
            }
        }
    }
}
