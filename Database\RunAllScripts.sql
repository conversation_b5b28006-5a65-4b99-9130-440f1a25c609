-- ===================================================================
-- سكريبت تشغيل جميع سكريبتات قاعدة البيانات
-- نظام إدارة الأوقاف
-- ===================================================================

PRINT N'بدء تشغيل سكريبتات قاعدة البيانات...';
PRINT N'======================================';

-- 1. إنشاء قاعدة البيانات
PRINT N'1. إنشاء قاعدة البيانات...';
GO

-- التحقق من وجود قاعدة البيانات
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'AwqafManagement')
BEGIN
    CREATE DATABASE [AwqafManagement]
    ON 
    ( NAME = N'AwqafManagement', 
      FILENAME = N'C:\Program Files\Microsoft SQL Server\MSSQL12.MSSQLSERVER\MSSQL\DATA\AwqafManagement.mdf',
      SIZE = 100MB, 
      MAXSIZE = UNLIMITED, 
      FILEGROWTH = 10MB )
    LOG ON 
    ( NAME = N'AwqafManagement_Log', 
      FILENAME = N'C:\Program Files\Microsoft SQL Server\MSSQL12.MSSQLSERVER\MSSQL\DATA\AwqafManagement_Log.ldf',
      SIZE = 10MB, 
      MAXSIZE = UNLIMITED, 
      FILEGROWTH = 10% );
    
    -- تعيين الترتيب العربي
    ALTER DATABASE [AwqafManagement] COLLATE Arabic_CI_AS;
    
    PRINT N'تم إنشاء قاعدة البيانات بنجاح';
END
ELSE
BEGIN
    PRINT N'قاعدة البيانات موجودة مسبقاً';
END
GO

-- الانتقال إلى قاعدة البيانات
USE [AwqafManagement];
GO

-- 2. إنشاء جداول الأمان
PRINT N'2. إنشاء جداول الأمان...';
GO

-- جدول الأدوار
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Roles]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Roles](
        [RoleId] [int] IDENTITY(1,1) NOT NULL,
        [RoleName] [nvarchar](50) NOT NULL,
        [RoleNameAr] [nvarchar](100) NOT NULL,
        [Description] [nvarchar](500) NULL,
        [IsActive] [bit] NOT NULL DEFAULT 1,
        [CreatedDate] [datetime] NOT NULL DEFAULT GETDATE(),
        [CreatedBy] [int] NULL,
        [ModifiedDate] [datetime] NULL,
        [ModifiedBy] [int] NULL,
        CONSTRAINT [PK_Roles] PRIMARY KEY CLUSTERED ([RoleId] ASC),
        CONSTRAINT [UK_Roles_RoleName] UNIQUE NONCLUSTERED ([RoleName] ASC)
    );
    PRINT N'تم إنشاء جدول الأدوار';
END

-- جدول الصلاحيات
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Permissions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Permissions](
        [PermissionId] [int] IDENTITY(1,1) NOT NULL,
        [PermissionName] [nvarchar](100) NOT NULL,
        [PermissionNameAr] [nvarchar](150) NOT NULL,
        [ModuleName] [nvarchar](50) NOT NULL,
        [ModuleNameAr] [nvarchar](100) NOT NULL,
        [Description] [nvarchar](500) NULL,
        [IsActive] [bit] NOT NULL DEFAULT 1,
        [CreatedDate] [datetime] NOT NULL DEFAULT GETDATE(),
        CONSTRAINT [PK_Permissions] PRIMARY KEY CLUSTERED ([PermissionId] ASC),
        CONSTRAINT [UK_Permissions_PermissionName] UNIQUE NONCLUSTERED ([PermissionName] ASC)
    );
    PRINT N'تم إنشاء جدول الصلاحيات';
END

-- جدول ربط الأدوار بالصلاحيات
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RolePermissions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[RolePermissions](
        [RolePermissionId] [int] IDENTITY(1,1) NOT NULL,
        [RoleId] [int] NOT NULL,
        [PermissionId] [int] NOT NULL,
        [CanView] [bit] NOT NULL DEFAULT 0,
        [CanAdd] [bit] NOT NULL DEFAULT 0,
        [CanEdit] [bit] NOT NULL DEFAULT 0,
        [CanDelete] [bit] NOT NULL DEFAULT 0,
        [CanPrint] [bit] NOT NULL DEFAULT 0,
        [CanExport] [bit] NOT NULL DEFAULT 0,
        [CreatedDate] [datetime] NOT NULL DEFAULT GETDATE(),
        [CreatedBy] [int] NULL,
        CONSTRAINT [PK_RolePermissions] PRIMARY KEY CLUSTERED ([RolePermissionId] ASC),
        CONSTRAINT [UK_RolePermissions] UNIQUE NONCLUSTERED ([RoleId] ASC, [PermissionId] ASC),
        CONSTRAINT [FK_RolePermissions_Roles] FOREIGN KEY([RoleId]) REFERENCES [dbo].[Roles] ([RoleId]),
        CONSTRAINT [FK_RolePermissions_Permissions] FOREIGN KEY([PermissionId]) REFERENCES [dbo].[Permissions] ([PermissionId])
    );
    PRINT N'تم إنشاء جدول ربط الأدوار بالصلاحيات';
END

-- جدول المستخدمين
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Users](
        [UserId] [int] IDENTITY(1,1) NOT NULL,
        [Username] [nvarchar](50) NOT NULL,
        [PasswordHash] [nvarchar](255) NOT NULL,
        [Salt] [nvarchar](50) NOT NULL,
        [FullName] [nvarchar](100) NOT NULL,
        [Email] [nvarchar](100) NULL,
        [Phone] [nvarchar](20) NULL,
        [IsActive] [bit] NOT NULL DEFAULT 1,
        [IsLocked] [bit] NOT NULL DEFAULT 0,
        [FailedLoginAttempts] [int] NOT NULL DEFAULT 0,
        [LastLoginDate] [datetime] NULL,
        [LastPasswordChange] [datetime] NOT NULL DEFAULT GETDATE(),
        [PasswordExpiryDate] [datetime] NULL,
        [CreatedDate] [datetime] NOT NULL DEFAULT GETDATE(),
        [CreatedBy] [int] NULL,
        [ModifiedDate] [datetime] NULL,
        [ModifiedBy] [int] NULL,
        CONSTRAINT [PK_Users] PRIMARY KEY CLUSTERED ([UserId] ASC),
        CONSTRAINT [UK_Users_Username] UNIQUE NONCLUSTERED ([Username] ASC)
    );
    PRINT N'تم إنشاء جدول المستخدمين';
END

-- جدول ربط المستخدمين بالأدوار
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[UserRoles]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[UserRoles](
        [UserRoleId] [int] IDENTITY(1,1) NOT NULL,
        [UserId] [int] NOT NULL,
        [RoleId] [int] NOT NULL,
        [AssignedDate] [datetime] NOT NULL DEFAULT GETDATE(),
        [AssignedBy] [int] NULL,
        [IsActive] [bit] NOT NULL DEFAULT 1,
        CONSTRAINT [PK_UserRoles] PRIMARY KEY CLUSTERED ([UserRoleId] ASC),
        CONSTRAINT [UK_UserRoles] UNIQUE NONCLUSTERED ([UserId] ASC, [RoleId] ASC),
        CONSTRAINT [FK_UserRoles_Users] FOREIGN KEY([UserId]) REFERENCES [dbo].[Users] ([UserId]),
        CONSTRAINT [FK_UserRoles_Roles] FOREIGN KEY([RoleId]) REFERENCES [dbo].[Roles] ([RoleId])
    );
    PRINT N'تم إنشاء جدول ربط المستخدمين بالأدوار';
END

-- جدول سجل تسجيل الدخول
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[LoginHistory]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[LoginHistory](
        [LoginId] [int] IDENTITY(1,1) NOT NULL,
        [UserId] [int] NULL,
        [Username] [nvarchar](50) NOT NULL,
        [LoginDate] [datetime] NOT NULL DEFAULT GETDATE(),
        [IPAddress] [nvarchar](45) NULL,
        [UserAgent] [nvarchar](500) NULL,
        [IsSuccessful] [bit] NOT NULL,
        [FailureReason] [nvarchar](200) NULL,
        [LogoutDate] [datetime] NULL,
        CONSTRAINT [PK_LoginHistory] PRIMARY KEY CLUSTERED ([LoginId] ASC),
        CONSTRAINT [FK_LoginHistory_Users] FOREIGN KEY([UserId]) REFERENCES [dbo].[Users] ([UserId])
    );
    PRINT N'تم إنشاء جدول سجل تسجيل الدخول';
END

PRINT N'تم إنشاء جميع جداول الأمان بنجاح';
GO

-- 3. إدراج البيانات الأساسية
PRINT N'3. إدراج البيانات الأساسية...';
GO

-- إدراج الأدوار الأساسية
IF NOT EXISTS (SELECT * FROM Roles WHERE RoleName = 'SuperAdmin')
BEGIN
    INSERT INTO Roles (RoleName, RoleNameAr, Description) VALUES
    ('SuperAdmin', N'مدير النظام الرئيسي', N'صلاحيات كاملة على جميع أجزاء النظام'),
    ('Admin', N'مدير النظام', N'صلاحيات إدارية على معظم أجزاء النظام'),
    ('PropertyManager', N'مدير العقارات', N'إدارة العقارات والعقود والإيجارات'),
    ('AccountManager', N'مدير المحاسبة', N'إدارة الحسابات والمعاملات المالية'),
    ('DataEntry', N'مدخل بيانات', N'إدخال وتعديل البيانات الأساسية'),
    ('Viewer', N'مستعرض', N'عرض البيانات والتقارير فقط');
    
    PRINT N'تم إدراج الأدوار الأساسية';
END

-- إدراج الصلاحيات الأساسية
IF NOT EXISTS (SELECT * FROM Permissions WHERE PermissionName = 'UserManagement')
BEGIN
    INSERT INTO Permissions (PermissionName, PermissionNameAr, ModuleName, ModuleNameAr, Description) VALUES
    ('UserManagement', N'إدارة المستخدمين', 'Security', N'الأمان', N'إدارة المستخدمين والأدوار والصلاحيات'),
    ('RoleManagement', N'إدارة الأدوار', 'Security', N'الأمان', N'إدارة الأدوار والصلاحيات'),
    ('PropertyManagement', N'إدارة العقارات', 'Properties', N'العقارات', N'إدارة بيانات العقارات'),
    ('CustomerManagement', N'إدارة العملاء', 'Customers', N'العملاء', N'إدارة بيانات العملاء'),
    ('ContractManagement', N'إدارة العقود', 'Contracts', N'العقود', N'إدارة عقود البيع والإيجار'),
    ('PaymentManagement', N'إدارة المدفوعات', 'Payments', N'المدفوعات', N'إدارة المدفوعات والإيصالات'),
    ('AccountingManagement', N'إدارة المحاسبة', 'Accounting', N'المحاسبة', N'إدارة الحسابات والقيود المحاسبية'),
    ('InventoryManagement', N'إدارة المخازن', 'Inventory', N'المخازن', N'إدارة المخازن والمواد'),
    ('EmployeeManagement', N'إدارة الموظفين', 'Employees', N'الموظفين', N'إدارة بيانات الموظفين'),
    ('AssetManagement', N'إدارة الأصول', 'Assets', N'الأصول', N'إدارة الأصول الثابتة'),
    ('ReportsManagement', N'إدارة التقارير', 'Reports', N'التقارير', N'عرض وطباعة التقارير'),
    ('SystemSettings', N'إعدادات النظام', 'Settings', N'الإعدادات', N'إدارة إعدادات النظام العامة');
    
    PRINT N'تم إدراج الصلاحيات الأساسية';
END

-- إنشاء المستخدم الافتراضي
IF NOT EXISTS (SELECT * FROM Users WHERE Username = 'admin')
BEGIN
    DECLARE @Salt NVARCHAR(50) = NEWID();
    DECLARE @PasswordHash NVARCHAR(255) = CONVERT(NVARCHAR(255), HASHBYTES('SHA2_256', 'admin123' + @Salt), 2);

    INSERT INTO Users (Username, PasswordHash, Salt, FullName, Email, IsActive, CreatedBy) VALUES
    ('admin', @PasswordHash, @Salt, N'مدير النظام الرئيسي', '<EMAIL>', 1, 1);

    -- ربط المستخدم بدور مدير النظام الرئيسي
    INSERT INTO UserRoles (UserId, RoleId, AssignedBy) VALUES
    (1, 1, 1);

    -- ربط دور مدير النظام الرئيسي بجميع الصلاحيات
    INSERT INTO RolePermissions (RoleId, PermissionId, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CanExport, CreatedBy)
    SELECT 1, PermissionId, 1, 1, 1, 1, 1, 1, 1
    FROM Permissions;
    
    PRINT N'تم إنشاء المستخدم الافتراضي: admin / admin123';
END

-- ===================================================================
-- جداول الدليل المحاسبي
-- ===================================================================

-- جدول أنواع الحسابات
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[AccountTypes]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[AccountTypes](
        [AccountTypeId] [int] IDENTITY(1,1) NOT NULL,
        [TypeName] [nvarchar](50) NOT NULL,
        [TypeNameAr] [nvarchar](100) NOT NULL,
        [Description] [nvarchar](500) NULL,
        [IsActive] [bit] NOT NULL DEFAULT 1,
        [CreatedDate] [datetime2] NOT NULL DEFAULT GETDATE(),
        [CreatedBy] [int] NULL,
        [ModifiedDate] [datetime2] NULL,
        [ModifiedBy] [int] NULL,
        CONSTRAINT [PK_AccountTypes] PRIMARY KEY CLUSTERED ([AccountTypeId] ASC),
        CONSTRAINT [UK_AccountTypes_TypeName] UNIQUE NONCLUSTERED ([TypeName] ASC)
    );
    PRINT N'تم إنشاء جدول أنواع الحسابات';
END

-- جدول العملات
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Currencies]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Currencies](
        [CurrencyId] [int] IDENTITY(1,1) NOT NULL,
        [CurrencyCode] [nvarchar](10) NOT NULL,
        [CurrencyName] [nvarchar](50) NOT NULL,
        [CurrencyNameAr] [nvarchar](100) NOT NULL,
        [Symbol] [nvarchar](10) NOT NULL,
        [ExchangeRate] [decimal](18,6) NOT NULL DEFAULT 1.0,
        [IsBaseCurrency] [bit] NOT NULL DEFAULT 0,
        [IsActive] [bit] NOT NULL DEFAULT 1,
        [CreatedDate] [datetime2] NOT NULL DEFAULT GETDATE(),
        [CreatedBy] [int] NULL,
        [ModifiedDate] [datetime2] NULL,
        [ModifiedBy] [int] NULL,
        CONSTRAINT [PK_Currencies] PRIMARY KEY CLUSTERED ([CurrencyId] ASC),
        CONSTRAINT [UK_Currencies_Code] UNIQUE NONCLUSTERED ([CurrencyCode] ASC)
    );
    PRINT N'تم إنشاء جدول العملات';
END

-- جدول الدليل المحاسبي الرئيسي
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ChartOfAccounts]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[ChartOfAccounts](
        [AccountId] [int] IDENTITY(1,1) NOT NULL,
        [AccountCode] [nvarchar](20) NOT NULL,
        [AccountNameAr] [nvarchar](200) NOT NULL,
        [AccountNameEn] [nvarchar](200) NULL,
        [AccountTypeId] [int] NOT NULL,
        [ParentAccountId] [int] NULL,
        [Level] [int] NOT NULL DEFAULT 1,
        [CurrencyId] [int] NOT NULL,
        [OpeningBalance] [decimal](18,4) NOT NULL DEFAULT 0.0,
        [CurrentBalance] [decimal](18,4) NOT NULL DEFAULT 0.0,
        [IsActive] [bit] NOT NULL DEFAULT 1,
        [AllowTransactions] [bit] NOT NULL DEFAULT 1,
        [Description] [nvarchar](500) NULL,
        [CreatedDate] [datetime2] NOT NULL DEFAULT GETDATE(),
        [CreatedBy] [int] NULL,
        [ModifiedDate] [datetime2] NULL,
        [ModifiedBy] [int] NULL,
        CONSTRAINT [PK_ChartOfAccounts] PRIMARY KEY CLUSTERED ([AccountId] ASC),
        CONSTRAINT [UK_ChartOfAccounts_Code] UNIQUE NONCLUSTERED ([AccountCode] ASC),
        CONSTRAINT [FK_ChartOfAccounts_AccountType] FOREIGN KEY ([AccountTypeId]) REFERENCES [AccountTypes]([AccountTypeId]),
        CONSTRAINT [FK_ChartOfAccounts_Parent] FOREIGN KEY ([ParentAccountId]) REFERENCES [ChartOfAccounts]([AccountId]),
        CONSTRAINT [FK_ChartOfAccounts_Currency] FOREIGN KEY ([CurrencyId]) REFERENCES [Currencies]([CurrencyId])
    );
    PRINT N'تم إنشاء جدول الدليل المحاسبي';
END

-- جدول البيانات الشخصية للحسابات
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[AccountPersonalInfo]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[AccountPersonalInfo](
        [PersonalInfoId] [int] IDENTITY(1,1) NOT NULL,
        [AccountId] [int] NOT NULL,
        [ContactPersonName] [nvarchar](200) NULL,
        [Email] [nvarchar](100) NULL,
        [Phone] [nvarchar](20) NULL,
        [Mobile] [nvarchar](20) NULL,
        [Address] [nvarchar](500) NULL,
        [City] [nvarchar](100) NULL,
        [Country] [nvarchar](100) NULL,
        [PostalCode] [nvarchar](20) NULL,
        [IdentityNumber] [nvarchar](50) NULL,
        [TaxNumber] [nvarchar](50) NULL,
        [Notes] [nvarchar](1000) NULL,
        [CreatedDate] [datetime2] NOT NULL DEFAULT GETDATE(),
        [CreatedBy] [int] NULL,
        [ModifiedDate] [datetime2] NULL,
        [ModifiedBy] [int] NULL,
        CONSTRAINT [PK_AccountPersonalInfo] PRIMARY KEY CLUSTERED ([PersonalInfoId] ASC),
        CONSTRAINT [FK_AccountPersonalInfo_Account] FOREIGN KEY ([AccountId]) REFERENCES [ChartOfAccounts]([AccountId]) ON DELETE CASCADE,
        CONSTRAINT [UK_AccountPersonalInfo_AccountId] UNIQUE NONCLUSTERED ([AccountId] ASC)
    );
    PRINT N'تم إنشاء جدول البيانات الشخصية للحسابات';
END

-- جدول سجل تعديلات الحسابات
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[AccountAuditLog]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[AccountAuditLog](
        [AuditId] [int] IDENTITY(1,1) NOT NULL,
        [AccountId] [int] NOT NULL,
        [Action] [nvarchar](50) NOT NULL, -- INSERT, UPDATE, DELETE
        [FieldName] [nvarchar](100) NULL,
        [OldValue] [nvarchar](500) NULL,
        [NewValue] [nvarchar](500) NULL,
        [ActionDate] [datetime2] NOT NULL DEFAULT GETDATE(),
        [UserId] [int] NOT NULL,
        [UserName] [nvarchar](100) NOT NULL,
        [IPAddress] [nvarchar](45) NULL,
        [Description] [nvarchar](500) NULL,
        CONSTRAINT [PK_AccountAuditLog] PRIMARY KEY CLUSTERED ([AuditId] ASC),
        CONSTRAINT [FK_AccountAuditLog_Account] FOREIGN KEY ([AccountId]) REFERENCES [ChartOfAccounts]([AccountId]),
        CONSTRAINT [FK_AccountAuditLog_User] FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId])
    );
    PRINT N'تم إنشاء جدول سجل تعديلات الحسابات';
END

-- إنشاء الفهارس للأداء الأمثل
CREATE INDEX [IX_ChartOfAccounts_AccountType] ON [ChartOfAccounts]([AccountTypeId]);
CREATE INDEX [IX_ChartOfAccounts_Parent] ON [ChartOfAccounts]([ParentAccountId]);
CREATE INDEX [IX_ChartOfAccounts_Currency] ON [ChartOfAccounts]([CurrencyId]);
CREATE INDEX [IX_ChartOfAccounts_Level] ON [ChartOfAccounts]([Level]);
CREATE INDEX [IX_ChartOfAccounts_IsActive] ON [ChartOfAccounts]([IsActive]);
CREATE INDEX [IX_AccountPersonalInfo_Account] ON [AccountPersonalInfo]([AccountId]);
CREATE INDEX [IX_AccountAuditLog_Account] ON [AccountAuditLog]([AccountId]);
CREATE INDEX [IX_AccountAuditLog_ActionDate] ON [AccountAuditLog]([ActionDate]);
CREATE INDEX [IX_AccountAuditLog_User] ON [AccountAuditLog]([UserId]);

PRINT N'تم إنشاء الفهارس للدليل المحاسبي';

-- إدراج البيانات الأساسية لأنواع الحسابات
IF NOT EXISTS (SELECT 1 FROM AccountTypes WHERE TypeName = 'Assets')
BEGIN
    INSERT INTO AccountTypes (TypeName, TypeNameAr, Description) VALUES
    ('Assets', N'الأصول', N'جميع أنواع الأصول (ثابتة ومتداولة)'),
    ('Liabilities', N'الخصوم', N'جميع أنواع الخصوم والالتزامات'),
    ('Equity', N'حقوق الملكية', N'رأس المال وحقوق الملكية'),
    ('Revenue', N'الإيرادات', N'جميع أنواع الإيرادات والدخل'),
    ('Expenses', N'المصروفات', N'جميع أنواع المصروفات والتكاليف'),
    ('FixedAssets', N'الأصول الثابتة', N'الأصول الثابتة طويلة الأجل'),
    ('CurrentAssets', N'الأصول المتداولة', N'الأصول قصيرة الأجل'),
    ('CurrentLiabilities', N'الخصوم المتداولة', N'الخصوم قصيرة الأجل'),
    ('LongTermLiabilities', N'الخصوم طويلة الأجل', N'الخصوم والالتزامات طويلة الأجل'),
    ('OperatingRevenue', N'إيرادات التشغيل', N'الإيرادات من الأنشطة الرئيسية'),
    ('OtherRevenue', N'إيرادات أخرى', N'الإيرادات من الأنشطة الثانوية'),
    ('OperatingExpenses', N'مصروفات التشغيل', N'المصروفات التشغيلية'),
    ('AdministrativeExpenses', N'مصروفات إدارية', N'المصروفات الإدارية والعمومية');

    PRINT N'تم إدراج أنواع الحسابات الأساسية';
END

-- إدراج البيانات الأساسية للعملات
IF NOT EXISTS (SELECT 1 FROM Currencies WHERE CurrencyCode = 'SAR')
BEGIN
    INSERT INTO Currencies (CurrencyCode, CurrencyName, CurrencyNameAr, Symbol, ExchangeRate, IsBaseCurrency) VALUES
    ('SAR', 'Saudi Riyal', N'ريال سعودي', N'ر.س', 1.0, 1),
    ('USD', 'US Dollar', N'دولار أمريكي', N'$', 3.75, 0),
    ('EUR', 'Euro', N'يورو', N'€', 4.10, 0),
    ('GBP', 'British Pound', N'جنيه إسترليني', N'£', 4.70, 0),
    ('AED', 'UAE Dirham', N'درهم إماراتي', N'د.إ', 1.02, 0),
    ('KWD', 'Kuwaiti Dinar', N'دينار كويتي', N'د.ك', 12.25, 0),
    ('QAR', 'Qatari Riyal', N'ريال قطري', N'ر.ق', 1.03, 0),
    ('BHD', 'Bahraini Dinar', N'دينار بحريني', N'د.ب', 9.95, 0),
    ('OMR', 'Omani Rial', N'ريال عماني', N'ر.ع', 9.75, 0),
    ('JOD', 'Jordanian Dinar', N'دينار أردني', N'د.أ', 5.30, 0);

    PRINT N'تم إدراج العملات الأساسية';
END

-- تشغيل الإجراءات المخزنة للدليل المحاسبي
PRINT N'5. إنشاء الإجراءات المخزنة للدليل المحاسبي...';
GO
:r Database\05_AccountingStoredProcedures.sql
GO

-- إضافة صلاحيات الدليل المحاسبي
IF NOT EXISTS (SELECT 1 FROM Permissions WHERE PermissionName = 'ChartOfAccountsManagement')
BEGIN
    INSERT INTO Permissions (PermissionName, PermissionNameAr, ModuleName, ModuleNameAr, Description) VALUES
    ('ChartOfAccountsManagement', N'إدارة الدليل المحاسبي', 'Accounting', N'المحاسبة', N'إدارة الدليل المحاسبي والحسابات'),
    ('AccountPersonalInfoManagement', N'إدارة البيانات الشخصية للحسابات', 'Accounting', N'المحاسبة', N'إدارة البيانات الشخصية المرتبطة بالحسابات'),
    ('AccountAuditLogView', N'عرض سجل تعديلات الحسابات', 'Accounting', N'المحاسبة', N'عرض سجل التعديلات والتغييرات على الحسابات');

    PRINT N'تم إضافة صلاحيات الدليل المحاسبي';

    -- ربط صلاحيات الدليل المحاسبي بدور مدير النظام
    INSERT INTO RolePermissions (RoleId, PermissionId, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CanExport, CreatedBy)
    SELECT 1, p.PermissionId, 1, 1, 1, 1, 1, 1, 1
    FROM Permissions p
    WHERE p.PermissionName IN ('ChartOfAccountsManagement', 'AccountPersonalInfoManagement', 'AccountAuditLogView');

    PRINT N'تم ربط صلاحيات الدليل المحاسبي بدور مدير النظام';
END

PRINT N'======================================';
PRINT N'تم تشغيل جميع السكريبتات بنجاح!';
PRINT N'يمكنك الآن تسجيل الدخول باستخدام:';
PRINT N'اسم المستخدم: admin';
PRINT N'كلمة المرور: admin123';
PRINT N'======================================';
GO
