using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using Awqaf_Managment.Models.Accounting;

namespace Awqaf_Managment.Common.Helpers
{
    /// <summary>
    /// مساعد تصدير البيانات إلى Excel
    /// </summary>
    public static class ExcelHelper
    {
        /// <summary>
        /// تصدير الحسابات إلى Excel
        /// </summary>
        public static void ExportAccountsToExcel(List<Account> accounts, string fileName = null)
        {
            try
            {
                if (accounts == null || !accounts.Any())
                {
                    UIHelper.ShowWarning("لا توجد بيانات للتصدير");
                    return;
                }

                // إنشاء جدول البيانات
                var dataTable = CreateAccountsDataTable(accounts);

                // تصدير إلى Excel
                ExportDataTableToExcel(dataTable, fileName ?? "الدليل_المحاسبي", "الدليل المحاسبي");
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تصدير البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// تصدير سجل التعديلات إلى Excel
        /// </summary>
        public static void ExportAuditLogToExcel(List<AccountAuditLog> auditLogs, string fileName = null)
        {
            try
            {
                if (auditLogs == null || !auditLogs.Any())
                {
                    UIHelper.ShowWarning("لا توجد بيانات للتصدير");
                    return;
                }

                // إنشاء جدول البيانات
                var dataTable = CreateAuditLogDataTable(auditLogs);

                // تصدير إلى Excel
                ExportDataTableToExcel(dataTable, fileName ?? "سجل_التعديلات", "سجل تعديلات الحسابات");
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تصدير البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء جدول بيانات للحسابات
        /// </summary>
        private static DataTable CreateAccountsDataTable(List<Account> accounts)
        {
            var dataTable = new DataTable();

            // إضافة الأعمدة
            dataTable.Columns.Add("رمز الحساب", typeof(string));
            dataTable.Columns.Add("اسم الحساب (عربي)", typeof(string));
            dataTable.Columns.Add("اسم الحساب (إنجليزي)", typeof(string));
            dataTable.Columns.Add("نوع الحساب", typeof(string));
            dataTable.Columns.Add("الحساب الأب", typeof(string));
            dataTable.Columns.Add("المستوى", typeof(int));
            dataTable.Columns.Add("العملة", typeof(string));
            dataTable.Columns.Add("الرصيد الافتتاحي", typeof(decimal));
            dataTable.Columns.Add("الرصيد الحالي", typeof(decimal));
            dataTable.Columns.Add("حالة النشاط", typeof(string));
            dataTable.Columns.Add("السماح بالحركات", typeof(string));
            dataTable.Columns.Add("الوصف", typeof(string));
            dataTable.Columns.Add("تاريخ الإنشاء", typeof(DateTime));

            // إضافة البيانات
            foreach (var account in accounts.OrderBy(a => a.AccountCode))
            {
                var row = dataTable.NewRow();
                row["رمز الحساب"] = account.AccountCode;
                row["اسم الحساب (عربي)"] = account.AccountNameAr;
                row["اسم الحساب (إنجليزي)"] = account.AccountNameEn ?? "";
                row["نوع الحساب"] = account.AccountType?.TypeNameAr ?? "";
                row["الحساب الأب"] = account.ParentAccount?.AccountNameAr ?? "";
                row["المستوى"] = account.Level;
                row["العملة"] = account.Currency?.CurrencyNameAr ?? "";
                row["الرصيد الافتتاحي"] = account.OpeningBalance;
                row["الرصيد الحالي"] = account.CurrentBalance;
                row["حالة النشاط"] = account.IsActive ? "نشط" : "غير نشط";
                row["السماح بالحركات"] = account.AllowTransactions ? "مسموح" : "غير مسموح";
                row["الوصف"] = account.Description ?? "";
                row["تاريخ الإنشاء"] = account.CreatedDate;

                dataTable.Rows.Add(row);
            }

            return dataTable;
        }

        /// <summary>
        /// إنشاء جدول بيانات لسجل التعديلات
        /// </summary>
        private static DataTable CreateAuditLogDataTable(List<AccountAuditLog> auditLogs)
        {
            var dataTable = new DataTable();

            // إضافة الأعمدة
            dataTable.Columns.Add("تاريخ العملية", typeof(DateTime));
            dataTable.Columns.Add("نوع العملية", typeof(string));
            dataTable.Columns.Add("رمز الحساب", typeof(string));
            dataTable.Columns.Add("اسم الحساب", typeof(string));
            dataTable.Columns.Add("الحقل", typeof(string));
            dataTable.Columns.Add("القيمة القديمة", typeof(string));
            dataTable.Columns.Add("القيمة الجديدة", typeof(string));
            dataTable.Columns.Add("المستخدم", typeof(string));
            dataTable.Columns.Add("الوصف", typeof(string));

            // إضافة البيانات
            foreach (var log in auditLogs.OrderByDescending(l => l.ActionDate))
            {
                var row = dataTable.NewRow();
                row["تاريخ العملية"] = log.ActionDate;
                row["نوع العملية"] = log.ActionDisplay;
                row["رمز الحساب"] = log.Account?.AccountCode ?? "";
                row["اسم الحساب"] = log.Account?.AccountNameAr ?? "";
                row["الحقل"] = log.FieldName ?? "";
                row["القيمة القديمة"] = log.OldValue ?? "";
                row["القيمة الجديدة"] = log.NewValue ?? "";
                row["المستخدم"] = log.UserName;
                row["الوصف"] = log.Description ?? "";

                dataTable.Rows.Add(row);
            }

            return dataTable;
        }

        /// <summary>
        /// تصدير جدول البيانات إلى Excel
        /// </summary>
        private static void ExportDataTableToExcel(DataTable dataTable, string defaultFileName, string sheetName)
        {
            try
            {
                // اختيار مكان الحفظ
                using (var saveDialog = new SaveFileDialog())
                {
                    saveDialog.Filter = "Excel Files|*.xlsx|CSV Files|*.csv";
                    saveDialog.Title = "حفظ الملف";
                    saveDialog.FileName = $"{defaultFileName}_{DateTime.Now:yyyy-MM-dd}";

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        var extension = Path.GetExtension(saveDialog.FileName).ToLower();

                        if (extension == ".csv")
                        {
                            ExportToCSV(dataTable, saveDialog.FileName);
                        }
                        else
                        {
                            // محاولة استخدام Excel إذا كان متوفراً، وإلا استخدام CSV
                            try
                            {
                                ExportToExcel(dataTable, saveDialog.FileName, sheetName);
                            }
                            catch
                            {
                                // في حالة عدم توفر Excel، استخدم CSV
                                var csvFileName = Path.ChangeExtension(saveDialog.FileName, ".csv");
                                ExportToCSV(dataTable, csvFileName);
                                UIHelper.ShowInfo($"تم حفظ الملف كـ CSV في:\n{csvFileName}");
                                return;
                            }
                        }

                        UIHelper.ShowSuccess($"تم تصدير البيانات بنجاح إلى:\n{saveDialog.FileName}");

                        // سؤال المستخدم إذا كان يريد فتح الملف
                        var result = MessageBox.Show(
                            "هل تريد فتح الملف الآن؟",
                            "تصدير ناجح",
                            MessageBoxButtons.YesNo,
                            MessageBoxIcon.Question,
                            MessageBoxDefaultButton.Button1,
                            MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);

                        if (result == DialogResult.Yes)
                        {
                            System.Diagnostics.Process.Start(saveDialog.FileName);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تصدير الملف: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تصدير إلى Excel باستخدام COM
        /// </summary>
        private static void ExportToExcel(DataTable dataTable, string fileName, string sheetName)
        {
            try
            {
                // محاولة استخدام Excel COM
                dynamic excel = Activator.CreateInstance(Type.GetTypeFromProgID("Excel.Application"));
                dynamic workbook = excel.Workbooks.Add();
                dynamic worksheet = workbook.Worksheets[1];
                worksheet.Name = sheetName;

                // إضافة العناوين
                for (int i = 0; i < dataTable.Columns.Count; i++)
                {
                    worksheet.Cells[1, i + 1] = dataTable.Columns[i].ColumnName;
                }

                // تنسيق العناوين
                dynamic headerRange = worksheet.Range[worksheet.Cells[1, 1], worksheet.Cells[1, dataTable.Columns.Count]];
                headerRange.Font.Bold = true;
                headerRange.Interior.Color = System.Drawing.ColorTranslator.ToOle(System.Drawing.Color.LightBlue);

                // إضافة البيانات
                for (int i = 0; i < dataTable.Rows.Count; i++)
                {
                    for (int j = 0; j < dataTable.Columns.Count; j++)
                    {
                        worksheet.Cells[i + 2, j + 1] = dataTable.Rows[i][j]?.ToString() ?? "";
                    }
                }

                // تنسيق الأعمدة
                worksheet.Columns.AutoFit();

                // حفظ الملف
                workbook.SaveAs(fileName);
                workbook.Close();
                excel.Quit();

                // تنظيف الذاكرة
                System.Runtime.InteropServices.Marshal.ReleaseComObject(worksheet);
                System.Runtime.InteropServices.Marshal.ReleaseComObject(workbook);
                System.Runtime.InteropServices.Marshal.ReleaseComObject(excel);
            }
            catch
            {
                throw new Exception("Excel غير متوفر على هذا الجهاز");
            }
        }

        /// <summary>
        /// تصدير إلى CSV
        /// </summary>
        private static void ExportToCSV(DataTable dataTable, string fileName)
        {
            using (var writer = new StreamWriter(fileName, false, System.Text.Encoding.UTF8))
            {
                // كتابة العناوين
                var headers = dataTable.Columns.Cast<DataColumn>().Select(column => $"\"{column.ColumnName}\"");
                writer.WriteLine(string.Join(",", headers));

                // كتابة البيانات
                foreach (DataRow row in dataTable.Rows)
                {
                    var fields = row.ItemArray.Select(field => $"\"{field?.ToString().Replace("\"", "\"\"")}\"");
                    writer.WriteLine(string.Join(",", fields));
                }
            }
        }
    }
}
