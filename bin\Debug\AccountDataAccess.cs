using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using Awqaf_Managment.Models.Accounting;

namespace Awqaf_Managment.DataAccess.Accounting
{
    /// <summary>
    /// طبقة الوصول لبيانات الحسابات المحاسبية
    /// </summary>
    public static class AccountDataAccess
    {
        /// <summary>
        /// الحصول على جميع الحسابات
        /// </summary>
        public static List<Account> GetAllAccounts()
        {
            var accounts = new List<Account>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand("SP_SearchAccounts", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                accounts.Add(MapReaderToAccount(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع الحسابات: {ex.Message}", ex);
            }

            return accounts;
        }

        /// <summary>
        /// البحث في الحسابات
        /// </summary>
        public static List<Account> SearchAccounts(string searchTerm = null, int? accountTypeId = null, 
            int? parentAccountId = null, bool? isActive = null, bool? allowTransactions = null)
        {
            var accounts = new List<Account>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand("SP_SearchAccounts", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        
                        // إضافة المعاملات
                        command.Parameters.AddWithValue("@SearchTerm", (object)searchTerm ?? DBNull.Value);
                        command.Parameters.AddWithValue("@AccountTypeId", (object)accountTypeId ?? DBNull.Value);
                        command.Parameters.AddWithValue("@ParentAccountId", (object)parentAccountId ?? DBNull.Value);
                        command.Parameters.AddWithValue("@IsActive", (object)isActive ?? DBNull.Value);
                        command.Parameters.AddWithValue("@AllowTransactions", (object)allowTransactions ?? DBNull.Value);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                accounts.Add(MapReaderToAccount(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث في الحسابات: {ex.Message}", ex);
            }

            return accounts;
        }

        /// <summary>
        /// الحصول على الحسابات الهرمية
        /// </summary>
        public static List<Account> GetAccountsHierarchy(int? parentAccountId = null)
        {
            var accounts = new List<Account>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand("SP_GetAccountsHierarchy", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@ParentAccountId", (object)parentAccountId ?? DBNull.Value);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var account = MapReaderToAccount(reader);
                                
                                // إضافة المسار الهرمي إذا كان متوفراً
                                if (reader["HierarchyPath"] != DBNull.Value)
                                    account.HierarchyPath = reader["HierarchyPath"].ToString();
                                
                                accounts.Add(account);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع الحسابات الهرمية: {ex.Message}", ex);
            }

            return accounts;
        }

        /// <summary>
        /// الحصول على حساب بالمعرف
        /// </summary>
        public static Account GetAccountById(int accountId)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(@"
                        SELECT 
                            a.AccountId, a.AccountCode, a.AccountNameAr, a.AccountNameEn,
                            a.AccountTypeId, at.TypeNameAr AS AccountTypeName,
                            a.ParentAccountId, pa.AccountNameAr AS ParentAccountName,
                            a.Level, a.CurrencyId, c.CurrencyNameAr AS CurrencyName, c.Symbol AS CurrencySymbol,
                            a.OpeningBalance, a.CurrentBalance, a.IsActive, a.AllowTransactions,
                            a.Description, a.CreatedDate, a.ModifiedDate
                        FROM ChartOfAccounts a
                        INNER JOIN AccountTypes at ON a.AccountTypeId = at.AccountTypeId
                        LEFT JOIN ChartOfAccounts pa ON a.ParentAccountId = pa.AccountId
                        INNER JOIN Currencies c ON a.CurrencyId = c.CurrencyId
                        WHERE a.AccountId = @AccountId", connection))
                    {
                        command.Parameters.AddWithValue("@AccountId", accountId);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return MapReaderToAccount(reader);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع الحساب: {ex.Message}", ex);
            }

            return null;
        }

        /// <summary>
        /// إضافة حساب جديد
        /// </summary>
        public static int InsertAccount(Account account, int createdBy)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand("SP_InsertAccount", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        
                        // إضافة المعاملات
                        command.Parameters.AddWithValue("@AccountCode", account.AccountCode);
                        command.Parameters.AddWithValue("@AccountNameAr", account.AccountNameAr);
                        command.Parameters.AddWithValue("@AccountNameEn", (object)account.AccountNameEn ?? DBNull.Value);
                        command.Parameters.AddWithValue("@AccountTypeId", account.AccountTypeId);
                        command.Parameters.AddWithValue("@ParentAccountId", (object)account.ParentAccountId ?? DBNull.Value);
                        command.Parameters.AddWithValue("@CurrencyId", account.CurrencyId);
                        command.Parameters.AddWithValue("@OpeningBalance", account.OpeningBalance);
                        command.Parameters.AddWithValue("@IsActive", account.IsActive);
                        command.Parameters.AddWithValue("@AllowTransactions", account.AllowTransactions);
                        command.Parameters.AddWithValue("@Description", (object)account.Description ?? DBNull.Value);
                        command.Parameters.AddWithValue("@CreatedBy", createdBy);
                        
                        // معامل الإخراج
                        var outputParam = new SqlParameter("@NewAccountId", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        command.Parameters.Add(outputParam);
                        
                        command.ExecuteNonQuery();
                        
                        return (int)outputParam.Value;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة الحساب: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث حساب
        /// </summary>
        public static void UpdateAccount(Account account, int modifiedBy)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand("SP_UpdateAccount", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        
                        // إضافة المعاملات
                        command.Parameters.AddWithValue("@AccountId", account.AccountId);
                        command.Parameters.AddWithValue("@AccountCode", account.AccountCode);
                        command.Parameters.AddWithValue("@AccountNameAr", account.AccountNameAr);
                        command.Parameters.AddWithValue("@AccountNameEn", (object)account.AccountNameEn ?? DBNull.Value);
                        command.Parameters.AddWithValue("@AccountTypeId", account.AccountTypeId);
                        command.Parameters.AddWithValue("@ParentAccountId", (object)account.ParentAccountId ?? DBNull.Value);
                        command.Parameters.AddWithValue("@CurrencyId", account.CurrencyId);
                        command.Parameters.AddWithValue("@OpeningBalance", account.OpeningBalance);
                        command.Parameters.AddWithValue("@IsActive", account.IsActive);
                        command.Parameters.AddWithValue("@AllowTransactions", account.AllowTransactions);
                        command.Parameters.AddWithValue("@Description", (object)account.Description ?? DBNull.Value);
                        command.Parameters.AddWithValue("@ModifiedBy", modifiedBy);
                        
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث الحساب: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حذف حساب
        /// </summary>
        public static void DeleteAccount(int accountId, int deletedBy)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand("SP_DeleteAccount", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        
                        command.Parameters.AddWithValue("@AccountId", accountId);
                        command.Parameters.AddWithValue("@DeletedBy", deletedBy);
                        
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف الحساب: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// التحقق من وجود رمز الحساب
        /// </summary>
        public static bool IsAccountCodeExists(string accountCode, int? excludeAccountId = null)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(@"
                        SELECT COUNT(*) FROM ChartOfAccounts 
                        WHERE AccountCode = @AccountCode 
                        AND (@ExcludeAccountId IS NULL OR AccountId != @ExcludeAccountId)", connection))
                    {
                        command.Parameters.AddWithValue("@AccountCode", accountCode);
                        command.Parameters.AddWithValue("@ExcludeAccountId", (object)excludeAccountId ?? DBNull.Value);
                        
                        return (int)command.ExecuteScalar() > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في التحقق من رمز الحساب: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحويل قارئ البيانات إلى كائن حساب
        /// </summary>
        private static Account MapReaderToAccount(SqlDataReader reader)
        {
            try
            {
                return new Account
                {
                    AccountId = (int)reader["AccountId"],
                    AccountCode = reader["AccountCode"].ToString(),
                    AccountNameAr = reader["AccountNameAr"].ToString(),
                    AccountNameEn = reader["AccountName"] != DBNull.Value ? reader["AccountName"].ToString() : null,
                    AccountTypeId = (int)reader["AccountTypeId"],
                    ParentAccountId = reader["ParentAccountId"] != DBNull.Value ? (int?)reader["ParentAccountId"] : null,
                    Level = (int)reader["AccountLevel"],
                    CurrencyId = reader["CurrencyId"] != DBNull.Value ? (int)reader["CurrencyId"] : 1,
                    OpeningBalance = reader["OpeningBalance"] != DBNull.Value ? (decimal)reader["OpeningBalance"] : 0,
                    CurrentBalance = reader["CurrentBalance"] != DBNull.Value ? (decimal)reader["CurrentBalance"] : 0,
                    IsActive = (bool)reader["IsActive"],
                    AllowTransactions = (bool)reader["AllowPosting"],
                    Description = reader["Description"] != DBNull.Value ? reader["Description"].ToString() : null,
                    CreatedDate = (DateTime)reader["CreatedDate"],
                    ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? (DateTime?)reader["ModifiedDate"] : null,

                    // خصائص إضافية للعرض - مبسطة
                    AccountType = new AccountType { TypeNameAr = "نوع الحساب" },
                    ParentAccount = null,
                    Currency = new Currency { CurrencyNameAr = "ريال سعودي", Symbol = "ر.س" }
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحويل بيانات الحساب: {ex.Message}", ex);
            }
        }
    }

    /// <summary>
    /// Extension methods for SqlDataReader
    /// </summary>
    public static class SqlDataReaderExtensions
    {
        public static bool HasColumn(this SqlDataReader reader, string columnName)
        {
            try
            {
                return reader.GetOrdinal(columnName) >= 0;
            }
            catch (IndexOutOfRangeException)
            {
                return false;
            }
        }
    }
}
