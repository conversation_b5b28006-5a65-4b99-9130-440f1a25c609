using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using Awqaf_Managment.Models.Accounting;

namespace Awqaf_Managment.DataAccess.Accounting
{
    /// <summary>
    /// طبقة الوصول لبيانات أنواع الحسابات
    /// </summary>
    public static class AccountTypeDataAccess
    {
        /// <summary>
        /// الحصول على جميع أنواع الحسابات
        /// </summary>
        public static List<AccountType> GetAllAccountTypes()
        {
            var accountTypes = new List<AccountType>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(@"
                        SELECT AccountTypeId, TypeName, TypeNameAr, Description, IsActive, CreatedDate, ModifiedDate
                        FROM AccountTypes 
                        WHERE IsActive = 1
                        ORDER BY TypeNameAr", connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                accountTypes.Add(new AccountType
                                {
                                    AccountTypeId = (int)reader["AccountTypeId"],
                                    TypeName = reader["TypeName"].ToString(),
                                    TypeNameAr = reader["TypeNameAr"].ToString(),
                                    Description = reader["Description"] != DBNull.Value ? reader["Description"].ToString() : null,
                                    IsActive = (bool)reader["IsActive"],
                                    CreatedDate = (DateTime)reader["CreatedDate"],
                                    ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? (DateTime?)reader["ModifiedDate"] : null
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع أنواع الحسابات: {ex.Message}", ex);
            }

            return accountTypes;
        }

        /// <summary>
        /// الحصول على نوع حساب بالمعرف
        /// </summary>
        public static AccountType GetAccountTypeById(int accountTypeId)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(@"
                        SELECT AccountTypeId, TypeName, TypeNameAr, Description, IsActive, CreatedDate, ModifiedDate
                        FROM AccountTypes 
                        WHERE AccountTypeId = @AccountTypeId", connection))
                    {
                        command.Parameters.AddWithValue("@AccountTypeId", accountTypeId);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new AccountType
                                {
                                    AccountTypeId = (int)reader["AccountTypeId"],
                                    TypeName = reader["TypeName"].ToString(),
                                    TypeNameAr = reader["TypeNameAr"].ToString(),
                                    Description = reader["Description"] != DBNull.Value ? reader["Description"].ToString() : null,
                                    IsActive = (bool)reader["IsActive"],
                                    CreatedDate = (DateTime)reader["CreatedDate"],
                                    ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? (DateTime?)reader["ModifiedDate"] : null
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع نوع الحساب: {ex.Message}", ex);
            }

            return null;
        }

        /// <summary>
        /// البحث في أنواع الحسابات
        /// </summary>
        public static List<AccountType> SearchAccountTypes(string searchTerm)
        {
            var accountTypes = new List<AccountType>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(@"
                        SELECT AccountTypeId, TypeName, TypeNameAr, Description, IsActive, CreatedDate, ModifiedDate
                        FROM AccountTypes 
                        WHERE IsActive = 1
                        AND (TypeName LIKE @SearchTerm OR TypeNameAr LIKE @SearchTerm OR Description LIKE @SearchTerm)
                        ORDER BY TypeNameAr", connection))
                    {
                        command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");
                        
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                accountTypes.Add(new AccountType
                                {
                                    AccountTypeId = (int)reader["AccountTypeId"],
                                    TypeName = reader["TypeName"].ToString(),
                                    TypeNameAr = reader["TypeNameAr"].ToString(),
                                    Description = reader["Description"] != DBNull.Value ? reader["Description"].ToString() : null,
                                    IsActive = (bool)reader["IsActive"],
                                    CreatedDate = (DateTime)reader["CreatedDate"],
                                    ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? (DateTime?)reader["ModifiedDate"] : null
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث في أنواع الحسابات: {ex.Message}", ex);
            }

            return accountTypes;
        }
    }
}
