using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using Awqaf_Managment.Common;
using Awqaf_Managment.Common.Helpers;
using Awqaf_Managment.Models.Accounting;
using Awqaf_Managment.Services.Accounting;

namespace Awqaf_Managment.UI.Forms.Accounting
{
    /// <summary>
    /// نموذج سجل تعديلات الحساب
    /// </summary>
    public partial class AccountAuditLogForm : Form
    {
        private readonly int _accountId;
        private Account _account;
        private List<AccountAuditLog> _auditLogs;

        public AccountAuditLogForm(int accountId)
        {
            InitializeComponent();
            _accountId = accountId;
            InitializeCustomComponents();
        }

        private void InitializeCustomComponents()
        {
            // تطبيق دعم RTL
            UIHelper.ApplyRTLSupport(this);
            
            // تطبيق الخط العربي
            UIHelper.ApplyArabicFont(this);
            
            // تخصيص الألوان
            this.BackColor = Constants.BackgroundColor;
            pnlHeader.BackColor = Constants.PrimaryColor;
            lblTitle.ForeColor = Constants.WhiteColor;
            lblAccountInfo.ForeColor = Constants.WhiteColor;
            
            // إعداد DataGridView
            SetupDataGridView();
            
            // إعداد فلتر نوع العملية
            SetupActionFilter();
            
            // إعداد التواريخ الافتراضية
            dtpFromDate.Value = DateTime.Now.AddMonths(-1);
            dtpToDate.Value = DateTime.Now;
        }

        private void SetupDataGridView()
        {
            // تخصيص مظهر DataGridView
            dgvAuditLog.EnableHeadersVisualStyles = false;
            dgvAuditLog.ColumnHeadersDefaultCellStyle.BackColor = Constants.PrimaryColor;
            dgvAuditLog.ColumnHeadersDefaultCellStyle.ForeColor = Constants.WhiteColor;
            dgvAuditLog.ColumnHeadersDefaultCellStyle.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Bold);
            dgvAuditLog.DefaultCellStyle.Font = new System.Drawing.Font("Tahoma", 11F);
            dgvAuditLog.AlternatingRowsDefaultCellStyle.BackColor = System.Drawing.Color.FromArgb(240, 240, 240);
            
            // تخصيص ارتفاع الصفوف
            dgvAuditLog.RowTemplate.Height = 30;
        }

        private void SetupActionFilter()
        {
            var actions = new List<object>
            {
                new { Value = "", Text = "جميع العمليات" },
                new { Value = "INSERT", Text = "إضافة" },
                new { Value = "UPDATE", Text = "تعديل" },
                new { Value = "DELETE", Text = "حذف" }
            };

            cmbAction.DataSource = actions;
            cmbAction.DisplayMember = "Text";
            cmbAction.ValueMember = "Value";
            cmbAction.SelectedIndex = 0;
        }

        private void AccountAuditLogForm_Load(object sender, EventArgs e)
        {
            try
            {
                LoadAccountInfo();
                LoadAuditLog();
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تحميل البيانات: {ex.Message}");
                this.Close();
            }
        }

        private void LoadAccountInfo()
        {
            try
            {
                _account = AccountService.GetAccountById(_accountId);
                if (_account == null)
                {
                    throw new Exception("الحساب غير موجود");
                }

                // عرض معلومات الحساب
                lblAccountInfo.Text = $"الحساب: {_account.DisplayName} | النوع: {_account.AccountType?.TypeNameAr} | الرصيد: {_account.FormattedCurrentBalance}";
                
                // تحديث العنوان
                this.Text = $"سجل تعديلات الحساب - {_account.DisplayName}";
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحميل بيانات الحساب: {ex.Message}", ex);
            }
        }

        private void LoadAuditLog()
        {
            try
            {
                this.Cursor = Cursors.WaitCursor;
                
                // تحميل سجل التعديلات
                _auditLogs = AccountService.GetAccountAuditLog(_accountId, 1000, 1);
                
                // ربط البيانات بـ DataGridView
                dgvAuditLog.DataSource = _auditLogs;
                
                // تحديث عدد السجلات
                UpdateRecordCount();
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تحميل سجل التعديلات: {ex.Message}");
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void UpdateRecordCount()
        {
            var visibleCount = dgvAuditLog.Rows.Count;
            lblTitle.Text = $"سجل تعديلات الحساب ({visibleCount} سجل)";
        }

        private void btnApplyFilter_Click(object sender, EventArgs e)
        {
            ApplyFilter();
        }

        private void ApplyFilter()
        {
            try
            {
                if (_auditLogs == null) return;

                var filteredLogs = _auditLogs.AsEnumerable();

                // فلترة حسب التاريخ
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);
                
                filteredLogs = filteredLogs.Where(log => log.ActionDate >= fromDate && log.ActionDate <= toDate);

                // فلترة حسب نوع العملية
                var selectedAction = cmbAction.SelectedValue?.ToString();
                if (!string.IsNullOrEmpty(selectedAction))
                {
                    filteredLogs = filteredLogs.Where(log => log.Action == selectedAction);
                }

                // تطبيق الفلتر
                dgvAuditLog.DataSource = filteredLogs.ToList();
                UpdateRecordCount();
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تطبيق الفلتر: {ex.Message}");
            }
        }

        private void btnClearFilter_Click(object sender, EventArgs e)
        {
            ClearFilter();
        }

        private void ClearFilter()
        {
            try
            {
                // إعادة تعيين الفلاتر
                dtpFromDate.Value = DateTime.Now.AddMonths(-1);
                dtpToDate.Value = DateTime.Now;
                cmbAction.SelectedIndex = 0;

                // عرض جميع السجلات
                dgvAuditLog.DataSource = _auditLogs;
                UpdateRecordCount();
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في مسح الفلتر: {ex.Message}");
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadAuditLog();
        }

        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                var visibleLogs = GetVisibleLogs();
                if (!visibleLogs.Any())
                {
                    UIHelper.ShowWarning("لا توجد بيانات للتصدير");
                    return;
                }

                // تصدير سجل التعديلات إلى Excel
                ExcelHelper.ExportAuditLogToExcel(visibleLogs, $"سجل_تعديلات_{_account?.AccountCode}");
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تصدير البيانات: {ex.Message}");
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// تحديث سجل التعديلات من الخارج
        /// </summary>
        public void RefreshAuditLog()
        {
            LoadAuditLog();
        }

        /// <summary>
        /// البحث في سجل التعديلات
        /// </summary>
        public void SearchInAuditLog(string searchTerm)
        {
            try
            {
                if (_auditLogs == null || string.IsNullOrWhiteSpace(searchTerm)) return;

                var searchResults = _auditLogs.Where(log =>
                    (!string.IsNullOrEmpty(log.FieldName) && log.FieldName.Contains(searchTerm)) ||
                    (!string.IsNullOrEmpty(log.OldValue) && log.OldValue.Contains(searchTerm)) ||
                    (!string.IsNullOrEmpty(log.NewValue) && log.NewValue.Contains(searchTerm)) ||
                    (!string.IsNullOrEmpty(log.Description) && log.Description.Contains(searchTerm)) ||
                    log.UserName.Contains(searchTerm)
                ).ToList();

                dgvAuditLog.DataSource = searchResults;
                UpdateRecordCount();
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في البحث: {ex.Message}");
            }
        }

        /// <summary>
        /// تصدير السجلات المرئية إلى قائمة
        /// </summary>
        public List<AccountAuditLog> GetVisibleLogs()
        {
            try
            {
                return dgvAuditLog.DataSource as List<AccountAuditLog> ?? new List<AccountAuditLog>();
            }
            catch
            {
                return new List<AccountAuditLog>();
            }
        }

        /// <summary>
        /// الحصول على إحصائيات سجل التعديلات
        /// </summary>
        public Dictionary<string, int> GetAuditStatistics()
        {
            try
            {
                if (_auditLogs == null) return new Dictionary<string, int>();

                return _auditLogs.GroupBy(log => log.Action)
                                .ToDictionary(g => g.Key, g => g.Count());
            }
            catch
            {
                return new Dictionary<string, int>();
            }
        }
    }
}
