using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using Awqaf_Managment.Models.Accounting;

namespace Awqaf_Managment.DataAccess.Accounting
{
    /// <summary>
    /// طبقة الوصول لبيانات الحسابات المحاسبية
    /// </summary>
    public static class AccountDataAccess
    {
        /// <summary>
        /// الحصول على جميع الحسابات
        /// </summary>
        public static List<Account> GetAllAccounts()
        {
            var accounts = new List<Account>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT
                            c.AccountId,
                            c.AccountCode,
                            c.AccountNameAr,
                            c.AccountNameEn,
                            c.AccountTypeId,
                            c.ParentAccountId,
                            c.AccountLevel,
                            c.CurrencyId,
                            c.OpeningBalance,
                            c.<PERSON>ce,
                            c.<PERSON>,
                            c.AllowPosting,
                            c.Description,
                            c.CreatedDate,
                            c.ModifiedDate,
                            at.TypeNameAr,
                            curr.CurrencyNameAr,
                            curr.CurrencyCode
                        FROM ChartOfAccounts c
                        LEFT JOIN AccountTypes at ON c.AccountTypeId = at.AccountTypeId
                        LEFT JOIN Currencies curr ON c.CurrencyId = curr.CurrencyId
                        WHERE c.IsActive = 1
                        ORDER BY c.AccountCode";

                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                accounts.Add(MapReaderToAccount(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع الحسابات: {ex.Message}", ex);
            }

            return accounts;
        }

        /// <summary>
        /// البحث في الحسابات
        /// </summary>
        public static List<Account> SearchAccounts(string searchTerm = null, int? accountTypeId = null, 
            int? parentAccountId = null, bool? isActive = null, bool? allowTransactions = null)
        {
            var accounts = new List<Account>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT
                            c.AccountId,
                            c.AccountCode,
                            c.AccountNameAr,
                            c.AccountNameEn,
                            c.AccountTypeId,
                            c.ParentAccountId,
                            c.AccountLevel,
                            c.CurrencyId,
                            c.OpeningBalance,
                            c.CurrentBalance,
                            c.IsActive,
                            c.AllowPosting,
                            c.Description,
                            c.CreatedDate,
                            c.ModifiedDate,
                            at.TypeNameAr,
                            curr.CurrencyNameAr,
                            curr.CurrencyCode
                        FROM ChartOfAccounts c
                        LEFT JOIN AccountTypes at ON c.AccountTypeId = at.AccountTypeId
                        LEFT JOIN Currencies curr ON c.CurrencyId = curr.CurrencyId
                        WHERE 1=1";

                    var parameters = new List<SqlParameter>();

                    if (!string.IsNullOrEmpty(searchTerm))
                    {
                        query += " AND (c.AccountCode LIKE @SearchTerm OR c.AccountNameAr LIKE @SearchTerm OR c.AccountNameEn LIKE @SearchTerm)";
                        parameters.Add(new SqlParameter("@SearchTerm", $"%{searchTerm}%"));
                    }

                    if (accountTypeId.HasValue)
                    {
                        query += " AND c.AccountTypeId = @AccountTypeId";
                        parameters.Add(new SqlParameter("@AccountTypeId", accountTypeId.Value));
                    }

                    if (parentAccountId.HasValue)
                    {
                        query += " AND c.ParentAccountId = @ParentAccountId";
                        parameters.Add(new SqlParameter("@ParentAccountId", parentAccountId.Value));
                    }

                    if (isActive.HasValue)
                    {
                        query += " AND c.IsActive = @IsActive";
                        parameters.Add(new SqlParameter("@IsActive", isActive.Value));
                    }

                    if (allowTransactions.HasValue)
                    {
                        query += " AND c.AllowPosting = @AllowPosting";
                        parameters.Add(new SqlParameter("@AllowPosting", allowTransactions.Value));
                    }

                    query += " ORDER BY c.AccountCode";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddRange(parameters.ToArray());

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                accounts.Add(MapReaderToAccount(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث في الحسابات: {ex.Message}", ex);
            }

            return accounts;
        }

        /// <summary>
        /// الحصول على الحسابات الهرمية
        /// </summary>
        public static List<Account> GetAccountsHierarchy(int? parentAccountId = null)
        {
            var accounts = new List<Account>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand("SP_GetAccountsHierarchy", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@ParentAccountId", (object)parentAccountId ?? DBNull.Value);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var account = MapReaderToAccount(reader);
                                
                                // إضافة المسار الهرمي إذا كان متوفراً
                                if (reader["HierarchyPath"] != DBNull.Value)
                                    account.HierarchyPath = reader["HierarchyPath"].ToString();
                                
                                accounts.Add(account);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع الحسابات الهرمية: {ex.Message}", ex);
            }

            return accounts;
        }

        /// <summary>
        /// الحصول على حساب بالمعرف
        /// </summary>
        public static Account GetAccountById(int accountId)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(@"
                        SELECT 
                            a.AccountId, a.AccountCode, a.AccountNameAr, a.AccountNameEn,
                            a.AccountTypeId, at.TypeNameAr AS AccountTypeName,
                            a.ParentAccountId, pa.AccountNameAr AS ParentAccountName,
                            a.Level, a.CurrencyId, c.CurrencyNameAr AS CurrencyName, c.Symbol AS CurrencySymbol,
                            a.OpeningBalance, a.CurrentBalance, a.IsActive, a.AllowTransactions,
                            a.Description, a.CreatedDate, a.ModifiedDate
                        FROM ChartOfAccounts a
                        INNER JOIN AccountTypes at ON a.AccountTypeId = at.AccountTypeId
                        LEFT JOIN ChartOfAccounts pa ON a.ParentAccountId = pa.AccountId
                        INNER JOIN Currencies c ON a.CurrencyId = c.CurrencyId
                        WHERE a.AccountId = @AccountId", connection))
                    {
                        command.Parameters.AddWithValue("@AccountId", accountId);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return MapReaderToAccount(reader);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع الحساب: {ex.Message}", ex);
            }

            return null;
        }

        /// <summary>
        /// إضافة حساب جديد
        /// </summary>
        public static int InsertAccount(Account account, int createdBy)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string insertQuery = @"
                        INSERT INTO ChartOfAccounts (
                            AccountCode,  AccountNameAr,AccountNameEn, AccountTypeId,
                            ParentAccountId, CurrencyId, AccountLevel, IsActive,
                            AllowPosting, Description, OpeningBalance, CreatedBy, CreatedDate
                        ) VALUES (
                            @AccountCode, @AccountNameAr, @AccountNameEn, @AccountTypeId,
                            @ParentAccountId, @CurrencyId, @AccountLevel, @IsActive,
                            @AllowPosting, @Description, @OpeningBalance, @CreatedBy, GETDATE()
                        );
                        SELECT SCOPE_IDENTITY();";

                    using (var command = new SqlCommand(insertQuery, connection))
                    {
                        // إضافة المعاملات
                        command.Parameters.AddWithValue("@AccountCode", account.AccountCode);
                        command.Parameters.AddWithValue("@AccountNameEn", (object)account.AccountNameEn ?? DBNull.Value);
                        command.Parameters.AddWithValue("@AccountNameAR", account.AccountNameAr);
                        command.Parameters.AddWithValue("@AccountTypeId", account.AccountTypeId);
                        command.Parameters.AddWithValue("@ParentAccountId", (object)account.ParentAccountId ?? DBNull.Value);
                        command.Parameters.AddWithValue("@CurrencyId", account.CurrencyId);

                        // حساب المستوى
                        int accountLevel = 1;
                        if (account.ParentAccountId.HasValue)
                        {
                            var parentAccount = GetAccountById(account.ParentAccountId.Value);
                            if (parentAccount != null)
                            {
                                accountLevel = parentAccount.Level + 1;
                            }
                        }
                        command.Parameters.AddWithValue("@AccountLevel", accountLevel);

                        command.Parameters.AddWithValue("@IsActive", account.IsActive);
                        command.Parameters.AddWithValue("@AllowPosting", account.AllowTransactions);
                        command.Parameters.AddWithValue("@Description", (object)account.Description ?? DBNull.Value);
                        command.Parameters.AddWithValue("@OpeningBalance", account.OpeningBalance);
                        command.Parameters.AddWithValue("@CreatedBy", createdBy);

                        var result = command.ExecuteScalar();
                        return Convert.ToInt32(result);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة الحساب: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// توليد رمز حساب جديد
        /// </summary>
        public static string GenerateAccountCode(int? accountTypeId = null, int? parentAccountId = null)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();

                    string query;
                    if (parentAccountId.HasValue)
                    {
                        // إنشاء رمز فرعي
                        query = @"
                            SELECT TOP 1
                                CASE
                                    WHEN MAX(CAST(RIGHT(AccountCode, 2) AS INT)) IS NULL
                                    THEN CONCAT(p.AccountCode, '01')
                                    ELSE CONCAT(p.AccountCode, FORMAT(MAX(CAST(RIGHT(c.AccountCode, 2) AS INT)) + 1, '00'))
                                END as NewCode
                            FROM ChartOfAccounts p
                            LEFT JOIN ChartOfAccounts c ON c.ParentAccountId = p.AccountId
                            WHERE p.AccountId = @ParentAccountId
                            GROUP BY p.AccountCode";
                    }
                    else
                    {
                        // إنشاء رمز رئيسي
                        query = @"
                            SELECT
                                CASE
                                    WHEN MAX(CAST(AccountCode AS INT)) IS NULL
                                    THEN '1000'
                                    ELSE CAST(MAX(CAST(AccountCode AS INT)) + 10 AS VARCHAR)
                                END as NewCode
                            FROM ChartOfAccounts
                            WHERE ParentAccountId IS NULL
                            AND ISNUMERIC(AccountCode) = 1";
                    }

                    using (var command = new SqlCommand(query, connection))
                    {
                        if (parentAccountId.HasValue)
                        {
                            command.Parameters.AddWithValue("@ParentAccountId", parentAccountId.Value);
                        }

                        var result = command.ExecuteScalar();
                        return result?.ToString() ?? "1000";
                    }
                }
            }
            catch (Exception)
            {
                // في حالة الخطأ، أنشئ رمز بسيط
                return DateTime.Now.ToString("yyyyMMddHHmmss");
            }
        }

        /// <summary>
        /// تحديث حساب
        /// </summary>
        public static void UpdateAccount(Account account, int modifiedBy)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string updateQuery = @"
                        UPDATE ChartOfAccounts SET
                            AccountCode = @AccountCode,
                            AccountNameAr = @AccountNameAr,
                            AccountNameEn = @AccountNameEn,
                            AccountTypeId = @AccountTypeId,
                            ParentAccountId = @ParentAccountId,
                            CurrencyId = @CurrencyId,
                            AccountLevel = @AccountLevel,
                            IsActive = @IsActive,
                            AllowPosting = @AllowPosting,
                            Description = @Description,
                            OpeningBalance = @OpeningBalance,
                            ModifiedBy = @ModifiedBy,
                            ModifiedDate = GETDATE()
                        WHERE AccountId = @AccountId";

                    using (var command = new SqlCommand(updateQuery, connection))
                    {
                        // إضافة المعاملات
                        command.Parameters.AddWithValue("@AccountId", account.AccountId);
                        command.Parameters.AddWithValue("@AccountCode", account.AccountCode);
                        command.Parameters.AddWithValue("@AccountNameEn", (object)account.AccountNameEn ?? DBNull.Value);
                        command.Parameters.AddWithValue("@AccountNameAr", account.AccountNameAr);
                        command.Parameters.AddWithValue("@AccountTypeId", account.AccountTypeId);
                        command.Parameters.AddWithValue("@ParentAccountId", (object)account.ParentAccountId ?? DBNull.Value);
                        command.Parameters.AddWithValue("@CurrencyId", account.CurrencyId);

                        // حساب المستوى
                        int accountLevel = 1;
                        if (account.ParentAccountId.HasValue)
                        {
                            var parentAccount = GetAccountById(account.ParentAccountId.Value);
                            if (parentAccount != null)
                            {
                                accountLevel = parentAccount.Level + 1;
                            }
                        }
                        command.Parameters.AddWithValue("@AccountLevel", accountLevel);

                        command.Parameters.AddWithValue("@IsActive", account.IsActive);
                        command.Parameters.AddWithValue("@AllowPosting", account.AllowTransactions);
                        command.Parameters.AddWithValue("@Description", (object)account.Description ?? DBNull.Value);
                        command.Parameters.AddWithValue("@OpeningBalance", account.OpeningBalance);
                        command.Parameters.AddWithValue("@ModifiedBy", modifiedBy);
                        
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث الحساب: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حذف حساب
        /// </summary>
        public static void DeleteAccount(int accountId, int deletedBy)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand("SP_DeleteAccount", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        
                        command.Parameters.AddWithValue("@AccountId", accountId);
                        command.Parameters.AddWithValue("@DeletedBy", deletedBy);
                        
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف الحساب: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// التحقق من وجود رمز الحساب
        /// </summary>
        public static bool IsAccountCodeExists(string accountCode, int? excludeAccountId = null)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(@"
                        SELECT COUNT(*) FROM ChartOfAccounts 
                        WHERE AccountCode = @AccountCode 
                        AND (@ExcludeAccountId IS NULL OR AccountId != @ExcludeAccountId)", connection))
                    {
                        command.Parameters.AddWithValue("@AccountCode", accountCode);
                        command.Parameters.AddWithValue("@ExcludeAccountId", (object)excludeAccountId ?? DBNull.Value);
                        
                        return (int)command.ExecuteScalar() > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في التحقق من رمز الحساب: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحويل قارئ البيانات إلى كائن حساب
        /// </summary>
        private static Account MapReaderToAccount(SqlDataReader reader)
        {
            try
            {
                return new Account
                {
                    AccountId = (int)reader["AccountId"],
                    AccountCode = reader["AccountCode"].ToString(),
                    AccountNameAr = reader["AccountNameAr"].ToString(),
                    AccountNameEn = reader["AccountNameEn"] != DBNull.Value ? reader["AccountNameEn"].ToString() : null,
                    AccountTypeId = (int)reader["AccountTypeId"],
                    ParentAccountId = reader["ParentAccountId"] != DBNull.Value ? (int?)reader["ParentAccountId"] : null,
                    Level = (int)reader["AccountLevel"],
                    CurrencyId = reader["CurrencyId"] != DBNull.Value ? (int)reader["CurrencyId"] : 1,
                    OpeningBalance = reader["OpeningBalance"] != DBNull.Value ? (decimal)reader["OpeningBalance"] : 0,
                    CurrentBalance = reader["CurrentBalance"] != DBNull.Value ? (decimal)reader["CurrentBalance"] : 0,
                    IsActive = (bool)reader["IsActive"],
                    AllowTransactions = (bool)reader["AllowPosting"],
                    Description = reader["Description"] != DBNull.Value ? reader["Description"].ToString() : null,
                    CreatedDate = (DateTime)reader["CreatedDate"],
                    ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? (DateTime?)reader["ModifiedDate"] : null,

                    // خصائص إضافية للعرض
                    AccountType = new AccountType
                    {
                        TypeNameAr = reader["TypeNameAr"] != DBNull.Value ? reader["TypeNameAr"].ToString() : "غير محدد"
                    },
                    ParentAccount = null,
                    Currency = new Currency
                    {
                        CurrencyNameAr = reader["CurrencyNameAr"] != DBNull.Value ? reader["CurrencyNameAr"].ToString() : "ريال سعودي",
                        Symbol = reader["CurrencyCode"] != DBNull.Value ? reader["CurrencyCode"].ToString() : "ر.س"
                    }
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحويل بيانات الحساب: {ex.Message}", ex);
            }
        }
    }

    /// <summary>
    /// Extension methods for SqlDataReader
    /// </summary>
    public static class SqlDataReaderExtensions
    {
        public static bool HasColumn(this SqlDataReader reader, string columnName)
        {
            try
            {
                return reader.GetOrdinal(columnName) >= 0;
            }
            catch (IndexOutOfRangeException)
            {
                return false;
            }
        }
    }
}
