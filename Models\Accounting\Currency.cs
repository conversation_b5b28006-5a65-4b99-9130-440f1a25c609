using System;

namespace Awqaf_Managment.Models.Accounting
{
    /// <summary>
    /// نموذج بيانات العملة
    /// </summary>
    public class Currency
    {
        public int CurrencyId { get; set; }
        public string CurrencyCode { get; set; }
        public string CurrencyName { get; set; }
        public string CurrencyNameAr { get; set; }
        public string Symbol { get; set; }
        public decimal ExchangeRate { get; set; }
        public bool IsBaseCurrency { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public int? ModifiedBy { get; set; }

        public Currency()
        {
            ExchangeRate = 1.0m;
            IsBaseCurrency = false;
            IsActive = true;
            CreatedDate = DateTime.Now;
        }

        /// <summary>
        /// عرض العملة للواجهة
        /// </summary>
        public string DisplayName => $"{CurrencyNameAr} ({Symbol})";

        /// <summary>
        /// عرض العملة مع الرمز
        /// </summary>
        public string DisplayWithCode => $"{CurrencyCode} - {CurrencyNameAr} ({Symbol})";

        /// <summary>
        /// تنسيق المبلغ بعملة
        /// </summary>
        public string FormatAmount(decimal amount)
        {
            return $"{amount:N2} {Symbol}";
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(CurrencyCode) && 
                   !string.IsNullOrWhiteSpace(CurrencyName) &&
                   !string.IsNullOrWhiteSpace(CurrencyNameAr) &&
                   !string.IsNullOrWhiteSpace(Symbol) &&
                   ExchangeRate > 0;
        }
    }
}
