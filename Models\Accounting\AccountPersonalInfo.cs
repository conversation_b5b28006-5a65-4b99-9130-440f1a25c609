using System;
using System.ComponentModel.DataAnnotations;

namespace Awqaf_Managment.Models.Accounting
{
    /// <summary>
    /// نموذج البيانات الشخصية للحساب
    /// </summary>
    public class AccountPersonalInfo
    {
        public int PersonalInfoId { get; set; }
        
        [Required(ErrorMessage = "معرف الحساب مطلوب")]
        public int AccountId { get; set; }
        
        [StringLength(200, ErrorMessage = "اسم الشخص المسؤول يجب ألا يزيد عن 200 حرف")]
        public string ContactPersonName { get; set; }
        
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(100, ErrorMessage = "البريد الإلكتروني يجب ألا يزيد عن 100 حرف")]
        public string Email { get; set; }
        
        [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
        [StringLength(20, ErrorMessage = "رقم الهاتف يجب ألا يزيد عن 20 رقم")]
        public string Phone { get; set; }
        
        [Phone(ErrorMessage = "رقم الجوال غير صحيح")]
        [StringLength(20, ErrorMessage = "رقم الجوال يجب ألا يزيد عن 20 رقم")]
        public string Mobile { get; set; }
        
        [StringLength(500, ErrorMessage = "العنوان يجب ألا يزيد عن 500 حرف")]
        public string Address { get; set; }
        
        [StringLength(100, ErrorMessage = "المدينة يجب ألا تزيد عن 100 حرف")]
        public string City { get; set; }
        
        [StringLength(100, ErrorMessage = "الدولة يجب ألا تزيد عن 100 حرف")]
        public string Country { get; set; }
        
        [StringLength(20, ErrorMessage = "الرمز البريدي يجب ألا يزيد عن 20 حرف")]
        public string PostalCode { get; set; }
        
        [StringLength(50, ErrorMessage = "رقم الهوية يجب ألا يزيد عن 50 حرف")]
        public string IdentityNumber { get; set; }
        
        [StringLength(50, ErrorMessage = "الرقم الضريبي يجب ألا يزيد عن 50 حرف")]
        public string TaxNumber { get; set; }
        
        [StringLength(1000, ErrorMessage = "الملاحظات يجب ألا تزيد عن 1000 حرف")]
        public string Notes { get; set; }
        
        public DateTime CreatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public int? ModifiedBy { get; set; }

        // خاصية التنقل
        public Account Account { get; set; }

        public AccountPersonalInfo()
        {
            CreatedDate = DateTime.Now;
        }

        /// <summary>
        /// عرض الاسم الكامل للعنوان
        /// </summary>
        public string FullAddress
        {
            get
            {
                var parts = new[] { Address, City, Country, PostalCode };
                return string.Join(", ", Array.FindAll(parts, s => !string.IsNullOrWhiteSpace(s)));
            }
        }

        /// <summary>
        /// عرض معلومات الاتصال
        /// </summary>
        public string ContactInfo
        {
            get
            {
                var contacts = new List<string>();
                if (!string.IsNullOrWhiteSpace(Phone))
                    contacts.Add($"هاتف: {Phone}");
                if (!string.IsNullOrWhiteSpace(Mobile))
                    contacts.Add($"جوال: {Mobile}");
                if (!string.IsNullOrWhiteSpace(Email))
                    contacts.Add($"إيميل: {Email}");
                
                return string.Join(" | ", contacts);
            }
        }

        /// <summary>
        /// التحقق من وجود بيانات شخصية
        /// </summary>
        public bool HasPersonalData
        {
            get
            {
                return !string.IsNullOrWhiteSpace(ContactPersonName) ||
                       !string.IsNullOrWhiteSpace(Email) ||
                       !string.IsNullOrWhiteSpace(Phone) ||
                       !string.IsNullOrWhiteSpace(Mobile) ||
                       !string.IsNullOrWhiteSpace(Address);
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        public bool IsValid()
        {
            return AccountId > 0;
        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        public bool IsEmailValid()
        {
            if (string.IsNullOrWhiteSpace(Email))
                return true; // البريد الإلكتروني اختياري

            try
            {
                var addr = new System.Net.Mail.MailAddress(Email);
                return addr.Address == Email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة رقم الهاتف
        /// </summary>
        public bool IsPhoneValid()
        {
            if (string.IsNullOrWhiteSpace(Phone))
                return true; // الهاتف اختياري

            // تحقق بسيط من أن الرقم يحتوي على أرقام فقط (مع السماح بالمسافات والشرطات)
            return System.Text.RegularExpressions.Regex.IsMatch(Phone, @"^[\d\s\-\+\(\)]+$");
        }

        /// <summary>
        /// التحقق من صحة رقم الجوال
        /// </summary>
        public bool IsMobileValid()
        {
            if (string.IsNullOrWhiteSpace(Mobile))
                return true; // الجوال اختياري

            // تحقق بسيط من أن الرقم يحتوي على أرقام فقط (مع السماح بالمسافات والشرطات)
            return System.Text.RegularExpressions.Regex.IsMatch(Mobile, @"^[\d\s\-\+\(\)]+$");
        }

        /// <summary>
        /// نسخ البيانات من كائن آخر
        /// </summary>
        public void CopyFrom(AccountPersonalInfo source)
        {
            if (source == null) return;

            ContactPersonName = source.ContactPersonName;
            Email = source.Email;
            Phone = source.Phone;
            Mobile = source.Mobile;
            Address = source.Address;
            City = source.City;
            Country = source.Country;
            PostalCode = source.PostalCode;
            IdentityNumber = source.IdentityNumber;
            TaxNumber = source.TaxNumber;
            Notes = source.Notes;
        }
    }
}
