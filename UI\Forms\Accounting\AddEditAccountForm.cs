using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using Awqaf_Managment.Common;
using Awqaf_Managment.Common.Helpers;
using Awqaf_Managment.Models.Accounting;
using Awqaf_Managment.Services.Accounting;
using Awqaf_Managment.Services.Security;

namespace Awqaf_Managment.UI.Forms.Accounting
{
    /// <summary>
    /// نموذج إضافة/تعديل الحساب
    /// </summary>
    public partial class AddEditAccountForm : Form
    {
        private readonly int? _accountId;
        private readonly int? _parentAccountId;
        private Account _currentAccount;
        private List<AccountType> _accountTypes;
        private List<Currency> _currencies;
        private List<Account> _parentAccounts;
        private bool _isEditMode;

        /// <summary>
        /// منشئ لإضافة حساب جديد
        /// </summary>
        public AddEditAccountForm(int? parentAccountId = null)
        {
            InitializeComponent();
            _parentAccountId = parentAccountId;
            _isEditMode = false;
            InitializeCustomComponents();
        }

        /// <summary>
        /// منشئ لتعديل حساب موجود
        /// </summary>
        public AddEditAccountForm(int accountId)
        {
            InitializeComponent();
            _accountId = accountId;
            _isEditMode = true;
            InitializeCustomComponents();
        }

        private void InitializeCustomComponents()
        {
            // تطبيق دعم RTL
            UIHelper.ApplyRTLSupport(this);
            
            // تطبيق الخط العربي
            UIHelper.ApplyArabicFont(this);
            
            // تخصيص الألوان
            this.BackColor = Constants.BackgroundColor;
            pnlHeader.BackColor = Constants.PrimaryColor;
            lblTitle.ForeColor = Constants.WhiteColor;
            
            // تحديث العنوان
            if (_isEditMode)
            {
                lblTitle.Text = "تعديل الحساب";
                this.Text = "تعديل الحساب";
            }
            else
            {
                lblTitle.Text = "إضافة حساب جديد";
                this.Text = "إضافة حساب جديد";
            }
        }

        private void AddEditAccountForm_Load(object sender, EventArgs e)
        {
            try
            {
                LoadData();

                if (_isEditMode)
                {
                    LoadAccountData();
                }
                else
                {
                    // في حالة الإضافة، توليد رمز حساب جديد
                    GenerateNewAccountCode();

                    if (_parentAccountId.HasValue)
                    {
                        // تحديد الحساب الأب المحدد مسبقاً
                        cmbParentAccount.SelectedValue = _parentAccountId.Value;
                        GenerateNewAccountCode(); // إعادة توليد الرمز بناءً على الحساب الأب
                    }
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تحميل البيانات: {ex.Message}");
                this.Close();
            }
        }

        private void GenerateNewAccountCode()
        {
            try
            {
                int? parentAccountId = null;
                if (cmbParentAccount.SelectedValue != null && (int)cmbParentAccount.SelectedValue > 0)
                {
                    parentAccountId = (int)cmbParentAccount.SelectedValue;
                }

                int? accountTypeId = null;
                if (cmbAccountType.SelectedValue != null)
                {
                    accountTypeId = (int)cmbAccountType.SelectedValue;
                }

                string newCode = AccountDataAccess.GenerateAccountCode(accountTypeId, parentAccountId);
                txtAccountCode.Text = newCode;
            }
            catch (Exception ex)
            {
                // في حالة الخطأ، استخدم رمز افتراضي
                txtAccountCode.Text = DateTime.Now.ToString("yyyyMMddHHmmss");
            }
        }

        private void LoadData()
        {
            try
            {
                // تحميل أنواع الحسابات
                _accountTypes = AccountService.GetAccountTypes();
                if (_accountTypes != null && _accountTypes.Count > 0)
                {
                    cmbAccountType.DataSource = _accountTypes;
                    cmbAccountType.DisplayMember = "TypeNameAr";
                    cmbAccountType.ValueMember = "AccountTypeId";
                    cmbAccountType.SelectedIndex = -1;
                }
                else
                {
                    UIHelper.ShowWarning("لا توجد أنواع حسابات متاحة");
                }

                // تحميل العملات
                _currencies = AccountService.GetCurrencies();
                if (_currencies != null && _currencies.Count > 0)
                {
                    cmbCurrency.DataSource = _currencies;
                    cmbCurrency.DisplayMember = "CurrencyNameAr";
                    cmbCurrency.ValueMember = "CurrencyId";

                    // تحديد العملة الأساسية كافتراضية
                    var baseCurrency = _currencies.FirstOrDefault(c => c.IsBaseCurrency);
                    if (baseCurrency != null)
                    {
                        cmbCurrency.SelectedValue = baseCurrency.CurrencyId;
                    }
                }
                else
                {
                    UIHelper.ShowWarning("لا توجد عملات متاحة");
                }

                // تحميل الحسابات الأب المحتملة
                LoadParentAccounts();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحميل البيانات الأساسية: {ex.Message}", ex);
            }
        }

        private void LoadParentAccounts()
        {
            try
            {
                // الحصول على جميع الحسابات ما عدا الحساب الحالي (في حالة التعديل)
                var allAccounts = AccountService.GetAllAccounts();

                if (allAccounts != null && allAccounts.Count > 0)
                {
                    if (_isEditMode && _accountId.HasValue)
                    {
                        // استبعاد الحساب الحالي وجميع الحسابات الفرعية له لتجنب المرجع الدائري
                        _parentAccounts = allAccounts.Where(a => a.AccountId != _accountId.Value)
                                                    .OrderBy(a => a.AccountCode)
                                                    .ToList();
                    }
                    else
                    {
                        _parentAccounts = allAccounts.OrderBy(a => a.AccountCode).ToList();
                    }

                    // إضافة خيار "بدون حساب أب"
                    var parentAccountsList = new List<Account>
                    {
                        new Account { AccountId = 0, AccountNameAr = "-- بدون حساب أب (حساب رئيسي) --" }
                    };
                    parentAccountsList.AddRange(_parentAccounts);

                    cmbParentAccount.DataSource = parentAccountsList;
                    cmbParentAccount.DisplayMember = "AccountNameAr";
                    cmbParentAccount.ValueMember = "AccountId";
                    cmbParentAccount.SelectedValue = 0; // بدون حساب أب كافتراضي
                }
                else
                {
                    // إذا لم توجد حسابات، أضف خيار "بدون حساب أب" فقط
                    var parentAccountsList = new List<Account>
                    {
                        new Account { AccountId = 0, AccountNameAr = "-- بدون حساب أب (حساب رئيسي) --" }
                    };

                    cmbParentAccount.DataSource = parentAccountsList;
                    cmbParentAccount.DisplayMember = "AccountNameAr";
                    cmbParentAccount.ValueMember = "AccountId";
                    cmbParentAccount.SelectedValue = 0;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحميل الحسابات الأب: {ex.Message}", ex);
            }
        }

        private bool IsDescendantAccount(int accountId, int ancestorId, List<Account> allAccounts)
        {
            var account = allAccounts.FirstOrDefault(a => a.AccountId == accountId);
            while (account?.ParentAccountId != null)
            {
                if (account.ParentAccountId == ancestorId)
                    return true;
                
                account = allAccounts.FirstOrDefault(a => a.AccountId == account.ParentAccountId);
            }
            return false;
        }

        private void LoadAccountData()
        {
            try
            {
                _currentAccount = AccountService.GetAccountById(_accountId.Value);
                if (_currentAccount == null)
                {
                    throw new Exception("الحساب غير موجود");
                }

                // تعبئة الحقول
                txtAccountCode.Text = _currentAccount.AccountCode;
                txtAccountNameAr.Text = _currentAccount.AccountNameAr;
                txtAccountNameEn.Text = _currentAccount.AccountNameEn;
                txtDescription.Text = _currentAccount.Description;
                numOpeningBalance.Value = _currentAccount.OpeningBalance;
                chkIsActive.Checked = _currentAccount.IsActive;
                chkAllowTransactions.Checked = _currentAccount.AllowTransactions;

                // تحديد نوع الحساب
                cmbAccountType.SelectedValue = _currentAccount.AccountTypeId;

                // تحديد العملة
                cmbCurrency.SelectedValue = _currentAccount.CurrencyId;

                // تحديد الحساب الأب
                if (_currentAccount.ParentAccountId.HasValue)
                {
                    cmbParentAccount.SelectedValue = _currentAccount.ParentAccountId.Value;
                }
                else
                {
                    cmbParentAccount.SelectedValue = 0;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحميل بيانات الحساب: {ex.Message}", ex);
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var account = CreateAccountFromInput();
                
                if (_isEditMode)
                {
                    account.AccountId = _accountId.Value;
                    AccountService.UpdateAccount(account, AuthenticationService.CurrentUser.UserId);
                }
                else
                {
                    AccountService.CreateAccount(account, AuthenticationService.CurrentUser.UserId);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في حفظ الحساب: {ex.Message}");
            }
        }

        private bool ValidateInput()
        {
            errorProvider.Clear();
            bool isValid = true;

            // التحقق من رمز الحساب
            if (string.IsNullOrWhiteSpace(txtAccountCode.Text))
            {
                errorProvider.SetError(txtAccountCode, "رمز الحساب مطلوب");
                isValid = false;
            }

            // التحقق من اسم الحساب العربي
            if (string.IsNullOrWhiteSpace(txtAccountNameAr.Text))
            {
                errorProvider.SetError(txtAccountNameAr, "اسم الحساب باللغة العربية مطلوب");
                isValid = false;
            }

            // التحقق من نوع الحساب
            if (cmbAccountType.SelectedValue == null || (int)cmbAccountType.SelectedValue <= 0)
            {
                errorProvider.SetError(cmbAccountType, "نوع الحساب مطلوب");
                isValid = false;
            }

            // التحقق من العملة
            if (cmbCurrency.SelectedValue == null || (int)cmbCurrency.SelectedValue <= 0)
            {
                errorProvider.SetError(cmbCurrency, "العملة مطلوبة");
                isValid = false;
            }

            return isValid;
        }

        private Account CreateAccountFromInput()
        {
            var account = new Account
            {
                AccountCode = txtAccountCode.Text.Trim(),
                AccountNameAr = txtAccountNameAr.Text.Trim(),
                AccountNameEn = string.IsNullOrWhiteSpace(txtAccountNameEn.Text) ? null : txtAccountNameEn.Text.Trim(),
                AccountTypeId = (int)cmbAccountType.SelectedValue,
                CurrencyId = (int)cmbCurrency.SelectedValue,
                OpeningBalance = numOpeningBalance.Value,
                IsActive = chkIsActive.Checked,
                AllowTransactions = chkAllowTransactions.Checked,
                Description = string.IsNullOrWhiteSpace(txtDescription.Text) ? null : txtDescription.Text.Trim()
            };

            // تحديد الحساب الأب
            if (cmbParentAccount.SelectedValue != null && (int)cmbParentAccount.SelectedValue > 0)
            {
                account.ParentAccountId = (int)cmbParentAccount.SelectedValue;
            }

            return account;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
