using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Awqaf_Managment.Common;
using Awqaf_Managment.Common.Helpers;
using Awqaf_Managment.Models.Accounting;
using Awqaf_Managment.Models.Security;
using Awqaf_Managment.Services.Accounting;
using Awqaf_Managment.Services.Security;

namespace Awqaf_Managment.UI.Forms.Accounting
{
    /// <summary>
    /// نموذج إدارة الدليل المحاسبي
    /// </summary>
    public partial class ChartOfAccountsForm : Form
    {
        private List<Account> _allAccounts;
        private Account _selectedAccount;
        private bool _isLoading;

        public ChartOfAccountsForm()
        {
            InitializeComponent();
            InitializeCustomComponents();
        }

        private void InitializeCustomComponents()
        {
            // تطبيق دعم RTL
            UIHelper.ApplyRTLSupport(this);
            
            // تطبيق الخط العربي
            UIHelper.ApplyArabicFont(this);
            
            // تخصيص الألوان
            this.BackColor = Constants.BackgroundColor;
            pnlHeader.BackColor = Constants.PrimaryColor;
            lblTitle.ForeColor = Constants.WhiteColor;
            
            // إعداد شجرة الحسابات
            SetupTreeView();
            
            // إعداد القائمة السياقية
            treeAccounts.ContextMenuStrip = contextMenuTree;
            
            // تعطيل الأزرار في البداية
            UpdateButtonStates();
        }

        private void SetupTreeView()
        {
            // إعداد الأيقونات
            imageList.Images.Add("folder", CreateDefaultIcon(Color.Orange));
            imageList.Images.Add("account", CreateDefaultIcon(Color.Blue));
            imageList.Images.Add("inactive", CreateDefaultIcon(Color.Gray));
            
            treeAccounts.ImageList = imageList;
            treeAccounts.ShowNodeToolTips = true;
        }

        private Image CreateDefaultIcon(Color color)
        {
            var bitmap = new Bitmap(16, 16);
            using (var g = Graphics.FromImage(bitmap))
            {
                g.FillEllipse(new SolidBrush(color), 2, 2, 12, 12);
            }
            return bitmap;
        }

        private void ChartOfAccountsForm_Load(object sender, EventArgs e)
        {
            try
            {
                // فحص قاعدة البيانات أولاً
                if (!DatabaseHelper.TestConnection())
                {
                    this.Close();
                    return;
                }

                if (!DatabaseHelper.CheckRequiredTables())
                {
                    this.Close();
                    return;
                }

                if (!DatabaseHelper.CheckBasicData())
                {
                    this.Close();
                    return;
                }

                LoadAccounts();
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تحميل الحسابات: {ex.Message}");
                this.Close();
            }
        }

        private void LoadAccounts()
        {
            try
            {
                _isLoading = true;
                
                // عرض مؤشر التحميل
                this.Cursor = Cursors.WaitCursor;
                
                // تحميل جميع الحسابات
                _allAccounts = AccountService.GetAllAccounts();
                
                // بناء شجرة الحسابات
                BuildAccountTree();
                
                // تحديث حالة الأزرار
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تحميل الحسابات: {ex.Message}");
            }
            finally
            {
                _isLoading = false;
                this.Cursor = Cursors.Default;
            }
        }

        private void BuildAccountTree()
        {
            treeAccounts.BeginUpdate();
            treeAccounts.Nodes.Clear();
            
            try
            {
                // الحصول على الحسابات الرئيسية (بدون حساب أب)
                var mainAccounts = _allAccounts.Where(a => a.ParentAccountId == null).OrderBy(a => a.AccountCode);
                
                foreach (var account in mainAccounts)
                {
                    var node = CreateAccountNode(account);
                    treeAccounts.Nodes.Add(node);
                    
                    // إضافة الحسابات الفرعية
                    AddChildNodes(node, account.AccountId);
                }
                
                // توسيع المستوى الأول
                foreach (TreeNode node in treeAccounts.Nodes)
                {
                    node.Expand();
                }
            }
            finally
            {
                treeAccounts.EndUpdate();
            }
        }

        private TreeNode CreateAccountNode(Account account)
        {
            var node = new TreeNode(account.DisplayName)
            {
                Tag = account,
                ToolTipText = GetAccountTooltip(account)
            };
            
            // تحديد الأيقونة حسب حالة الحساب
            if (!account.IsActive)
            {
                node.ImageKey = "inactive";
                node.SelectedImageKey = "inactive";
                node.ForeColor = Color.Gray;
            }
            else if (HasChildAccounts(account.AccountId))
            {
                node.ImageKey = "folder";
                node.SelectedImageKey = "folder";
            }
            else
            {
                node.ImageKey = "account";
                node.SelectedImageKey = "account";
            }
            
            return node;
        }

        private void AddChildNodes(TreeNode parentNode, int parentAccountId)
        {
            var childAccounts = _allAccounts.Where(a => a.ParentAccountId == parentAccountId).OrderBy(a => a.AccountCode);
            
            foreach (var account in childAccounts)
            {
                var childNode = CreateAccountNode(account);
                parentNode.Nodes.Add(childNode);
                
                // إضافة الحسابات الفرعية للحساب الفرعي (تكرار)
                AddChildNodes(childNode, account.AccountId);
            }
        }

        private bool HasChildAccounts(int accountId)
        {
            return _allAccounts.Any(a => a.ParentAccountId == accountId);
        }

        private string GetAccountTooltip(Account account)
        {
            return $"الرمز: {account.AccountCode}\n" +
                   $"النوع: {account.AccountType?.TypeNameAr}\n" +
                   $"العملة: {account.Currency?.CurrencyNameAr}\n" +
                   $"الرصيد: {account.FormattedCurrentBalance}\n" +
                   $"الحالة: {account.StatusDisplay}";
        }

        private void treeAccounts_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (_isLoading || e.Node?.Tag == null) return;
            
            _selectedAccount = (Account)e.Node.Tag;
            DisplayAccountDetails(_selectedAccount);
            UpdateButtonStates();
        }

        private void DisplayAccountDetails(Account account)
        {
            if (account == null)
            {
                groupAccountInfo.Visible = false;
                lblSelectAccount.Visible = true;
                return;
            }
            
            lblSelectAccount.Visible = false;
            groupAccountInfo.Visible = true;
            
            // عرض تفاصيل الحساب
            lblAccountCodeValue.Text = account.AccountCode;
            lblAccountNameArValue.Text = account.AccountNameAr;
            lblAccountNameEnValue.Text = account.AccountNameEn ?? "-";
            lblAccountTypeValue.Text = account.AccountType?.TypeNameAr ?? "-";
            lblParentAccountValue.Text = account.ParentAccount?.AccountNameAr ?? "حساب رئيسي";
            lblLevelValue.Text = account.Level.ToString();
            lblCurrencyValue.Text = account.Currency?.DisplayName ?? "-";
            lblOpeningBalanceValue.Text = account.FormattedOpeningBalance;
            lblCurrentBalanceValue.Text = account.FormattedCurrentBalance;
            
            // تلوين الرصيد حسب القيمة
            if (account.CurrentBalance > 0)
                lblCurrentBalanceValue.ForeColor = Constants.SuccessColor;
            else if (account.CurrentBalance < 0)
                lblCurrentBalanceValue.ForeColor = Constants.ErrorColor;
            else
                lblCurrentBalanceValue.ForeColor = Constants.TextColor;
        }

        private void UpdateButtonStates()
        {
            bool hasSelection = _selectedAccount != null;
            bool canEdit = hasSelection && AuthenticationService.HasPermission("ChartOfAccountsManagement", PermissionAction.Edit);
            bool canDelete = hasSelection && AuthenticationService.HasPermission("ChartOfAccountsManagement", PermissionAction.Delete) &&
                           _selectedAccount?.CanDelete() == true;
            
            btnEdit.Enabled = canEdit;
            btnDelete.Enabled = canDelete;
            btnPersonalInfo.Enabled = hasSelection;
            btnAuditLog.Enabled = hasSelection;
            
            // تحديث القائمة السياقية
            menuEditAccount.Enabled = canEdit;
            menuDeleteAccount.Enabled = canDelete;
            menuAddSubAccount.Enabled = hasSelection && AuthenticationService.HasPermission("ChartOfAccountsManagement", PermissionAction.Add);
            menuPersonalInfo.Enabled = hasSelection;
            menuAuditLog.Enabled = hasSelection;
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                if (!AuthenticationService.HasPermission("ChartOfAccountsManagement", PermissionAction.Add))
                {
                    UIHelper.ShowWarning("ليس لديك صلاحية إضافة حسابات جديدة");
                    return;
                }
                
                var form = new AddEditAccountForm();
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadAccounts();
                    UIHelper.ShowSuccess("تم إضافة الحساب بنجاح");
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في إضافة الحساب: {ex.Message}");
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            EditSelectedAccount();
        }

        private void EditSelectedAccount()
        {
            try
            {
                if (_selectedAccount == null)
                {
                    UIHelper.ShowWarning("يرجى اختيار حساب للتعديل");
                    return;
                }
                
                if (!AuthenticationService.HasPermission("ChartOfAccountsManagement", PermissionAction.Edit))
                {
                    UIHelper.ShowWarning("ليس لديك صلاحية تعديل الحسابات");
                    return;
                }
                
                var form = new AddEditAccountForm(_selectedAccount.AccountId);
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadAccounts();
                    UIHelper.ShowSuccess("تم تعديل الحساب بنجاح");
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تعديل الحساب: {ex.Message}");
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            DeleteSelectedAccount();
        }

        private void DeleteSelectedAccount()
        {
            try
            {
                if (_selectedAccount == null)
                {
                    UIHelper.ShowWarning("يرجى اختيار حساب للحذف");
                    return;
                }
                
                if (!AuthenticationService.HasPermission("ChartOfAccountsManagement", PermissionAction.Delete))
                {
                    UIHelper.ShowWarning("ليس لديك صلاحية حذف الحسابات");
                    return;
                }
                
                if (!_selectedAccount.CanDelete())
                {
                    UIHelper.ShowWarning("لا يمكن حذف هذا الحساب لوجود حسابات فرعية أو رصيد غير صفر");
                    return;
                }
                
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الحساب '{_selectedAccount.DisplayName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question,
                    MessageBoxDefaultButton.Button2,
                    MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
                
                if (result == DialogResult.Yes)
                {
                    AccountService.DeleteAccount(_selectedAccount.AccountId, AuthenticationService.CurrentUser.UserId);
                    LoadAccounts();
                    UIHelper.ShowSuccess("تم حذف الحساب بنجاح");
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في حذف الحساب: {ex.Message}");
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadAccounts();
        }

        private void btnPersonalInfo_Click(object sender, EventArgs e)
        {
            ShowPersonalInfo();
        }

        private void ShowPersonalInfo()
        {
            try
            {
                if (_selectedAccount == null)
                {
                    UIHelper.ShowWarning("يرجى اختيار حساب لعرض البيانات الشخصية");
                    return;
                }
                
                var form = new AccountPersonalInfoForm(_selectedAccount.AccountId);
                form.ShowDialog();
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في عرض البيانات الشخصية: {ex.Message}");
            }
        }

        private void btnAuditLog_Click(object sender, EventArgs e)
        {
            ShowAuditLog();
        }

        private void ShowAuditLog()
        {
            try
            {
                if (_selectedAccount == null)
                {
                    UIHelper.ShowWarning("يرجى اختيار حساب لعرض سجل التعديلات");
                    return;
                }
                
                var form = new AccountAuditLogForm(_selectedAccount.AccountId);
                form.ShowDialog();
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في عرض سجل التعديلات: {ex.Message}");
            }
        }

        private void btnExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                if (!AuthenticationService.HasPermission("ChartOfAccountsManagement", PermissionAction.Export))
                {
                    UIHelper.ShowWarning("ليس لديك صلاحية تصدير البيانات");
                    return;
                }

                if (_allAccounts == null || !_allAccounts.Any())
                {
                    UIHelper.ShowWarning("لا توجد بيانات للتصدير");
                    return;
                }

                // تصدير الحسابات إلى Excel
                ExcelHelper.ExportAccountsToExcel(_allAccounts);
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تصدير البيانات: {ex.Message}");
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            PerformSearch();
        }

        private void txtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                PerformSearch();
                e.Handled = true;
            }
        }

        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            // البحث التلقائي أثناء الكتابة
            PerformSearch();
        }

        private void PerformSearch()
        {
            try
            {
                string searchTerm = txtSearch.Text.Trim();

                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    BuildAccountTree();
                    return;
                }

                // البحث في الحسابات
                var searchResults = _allAccounts.Where(a =>
                    a.AccountCode.Contains(searchTerm) ||
                    a.AccountNameAr.Contains(searchTerm) ||
                    (!string.IsNullOrWhiteSpace(a.AccountNameEn) && a.AccountNameEn.Contains(searchTerm)) ||
                    (!string.IsNullOrWhiteSpace(a.Description) && a.Description.Contains(searchTerm))
                ).ToList();

                // بناء شجرة البحث
                BuildSearchTree(searchResults);
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في البحث: {ex.Message}");
            }
        }

        private void BuildSearchTree(List<Account> searchResults)
        {
            treeAccounts.BeginUpdate();
            treeAccounts.Nodes.Clear();

            try
            {
                foreach (var account in searchResults.OrderBy(a => a.AccountCode))
                {
                    var node = CreateAccountNode(account);
                    treeAccounts.Nodes.Add(node);
                }

                // توسيع جميع العقد
                treeAccounts.ExpandAll();
            }
            finally
            {
                treeAccounts.EndUpdate();
            }
        }

        private void btnClearSearch_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            BuildAccountTree();
        }

        private void treeAccounts_NodeMouseClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                treeAccounts.SelectedNode = e.Node;
                _selectedAccount = e.Node?.Tag as Account;
                UpdateButtonStates();
            }
        }

        // أحداث القائمة السياقية
        private void menuAddAccount_Click(object sender, EventArgs e)
        {
            btnAdd_Click(sender, e);
        }

        private void menuAddSubAccount_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedAccount == null)
                {
                    UIHelper.ShowWarning("يرجى اختيار حساب أب لإضافة حساب فرعي تحته");
                    return;
                }

                if (!AuthenticationService.HasPermission("ChartOfAccountsManagement", PermissionAction.Add))
                {
                    UIHelper.ShowWarning("ليس لديك صلاحية إضافة حسابات جديدة");
                    return;
                }

                var form = new AddEditAccountForm(parentAccountId: _selectedAccount.AccountId);
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadAccounts();
                    UIHelper.ShowSuccess("تم إضافة الحساب الفرعي بنجاح");
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في إضافة الحساب الفرعي: {ex.Message}");
            }
        }

        private void menuEditAccount_Click(object sender, EventArgs e)
        {
            EditSelectedAccount();
        }

        private void menuDeleteAccount_Click(object sender, EventArgs e)
        {
            DeleteSelectedAccount();
        }

        private void menuPersonalInfo_Click(object sender, EventArgs e)
        {
            ShowPersonalInfo();
        }

        private void menuAuditLog_Click(object sender, EventArgs e)
        {
            ShowAuditLog();
        }

        private void menuExpandAll_Click(object sender, EventArgs e)
        {
            treeAccounts.ExpandAll();
        }

        private void menuCollapseAll_Click(object sender, EventArgs e)
        {
            treeAccounts.CollapseAll();
        }

        /// <summary>
        /// تحديث الشجرة بعد إضافة أو تعديل حساب
        /// </summary>
        public void RefreshTree()
        {
            LoadAccounts();
        }

        /// <summary>
        /// اختيار حساب معين في الشجرة
        /// </summary>
        public void SelectAccount(int accountId)
        {
            var node = FindNodeByAccountId(treeAccounts.Nodes, accountId);
            if (node != null)
            {
                treeAccounts.SelectedNode = node;
                node.EnsureVisible();
            }
        }

        private TreeNode FindNodeByAccountId(TreeNodeCollection nodes, int accountId)
        {
            foreach (TreeNode node in nodes)
            {
                if (node.Tag is Account account && account.AccountId == accountId)
                    return node;

                var childNode = FindNodeByAccountId(node.Nodes, accountId);
                if (childNode != null)
                    return childNode;
            }
            return null;
        }
    }
}
