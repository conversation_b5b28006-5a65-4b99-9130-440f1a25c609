-- ===================================================================
-- سكريبت الإعداد السريع لقاعدة البيانات
-- ===================================================================

-- إنشاء قاعدة البيانات إذا لم تكن موجودة
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'AwqafManagement')
BEGIN
    CREATE DATABASE AwqafManagement;
    PRINT N'تم إنشاء قاعدة البيانات AwqafManagement';
END

-- استخدام قاعدة البيانات
USE AwqafManagement;

-- إنشاء الجداول الأساسية إذا لم تكن موجودة

-- جدول الأدوار
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Roles]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Roles](
        [RoleId] [int] IDENTITY(1,1) NOT NULL,
        [RoleName] [nvarchar](100) NOT NULL,
        [Description] [nvarchar](500) NULL,
        [IsActive] [bit] NOT NULL DEFAULT 1,
        [CreatedDate] [datetime] NOT NULL DEFAULT GETDATE(),
        [CreatedBy] [int] NULL,
        [ModifiedDate] [datetime] NULL,
        [ModifiedBy] [int] NULL,
        CONSTRAINT [PK_Roles] PRIMARY KEY CLUSTERED ([RoleId] ASC)
    );
    PRINT N'تم إنشاء جدول Roles';
END

-- جدول المستخدمين
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Users](
        [UserId] [int] IDENTITY(1,1) NOT NULL,
        [Username] [nvarchar](50) NOT NULL,
        [PasswordHash] [nvarchar](255) NOT NULL,
        [FullName] [nvarchar](100) NOT NULL,
        [Email] [nvarchar](100) NULL,
        [IsActive] [bit] NOT NULL DEFAULT 1,
        [LastLoginDate] [datetime] NULL,
        [FailedLoginAttempts] [int] NOT NULL DEFAULT 0,
        [IsLocked] [bit] NOT NULL DEFAULT 0,
        [CreatedDate] [datetime] NOT NULL DEFAULT GETDATE(),
        [CreatedBy] [int] NULL,
        [ModifiedDate] [datetime] NULL,
        [ModifiedBy] [int] NULL,
        CONSTRAINT [PK_Users] PRIMARY KEY CLUSTERED ([UserId] ASC),
        CONSTRAINT [UK_Users_Username] UNIQUE ([Username])
    );
    PRINT N'تم إنشاء جدول Users';
END

-- جدول الصلاحيات
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Permissions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Permissions](
        [PermissionId] [int] IDENTITY(1,1) NOT NULL,
        [PermissionName] [nvarchar](100) NOT NULL,
        [Description] [nvarchar](500) NULL,
        [Category] [nvarchar](50) NULL,
        [IsActive] [bit] NOT NULL DEFAULT 1,
        [CreatedDate] [datetime] NOT NULL DEFAULT GETDATE(),
        CONSTRAINT [PK_Permissions] PRIMARY KEY CLUSTERED ([PermissionId] ASC),
        CONSTRAINT [UK_Permissions_Name] UNIQUE ([PermissionName])
    );
    PRINT N'تم إنشاء جدول Permissions';
END

-- جدول أنواع الحسابات
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[AccountTypes]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[AccountTypes](
        [AccountTypeId] [int] IDENTITY(1,1) NOT NULL,
        [TypeName] [nvarchar](100) NOT NULL,
        [TypeNameEn] [nvarchar](100) NULL,
        [Description] [nvarchar](500) NULL,
        [IsActive] [bit] NOT NULL DEFAULT 1,
        [CreatedDate] [datetime] NOT NULL DEFAULT GETDATE(),
        [CreatedBy] [int] NULL,
        [ModifiedDate] [datetime] NULL,
        [ModifiedBy] [int] NULL,
        CONSTRAINT [PK_AccountTypes] PRIMARY KEY CLUSTERED ([AccountTypeId] ASC)
    );
    PRINT N'تم إنشاء جدول AccountTypes';
END

-- جدول العملات
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Currencies]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Currencies](
        [CurrencyId] [int] IDENTITY(1,1) NOT NULL,
        [CurrencyCode] [nvarchar](10) NOT NULL,
        [CurrencyName] [nvarchar](100) NOT NULL,
        [CurrencyNameEn] [nvarchar](100) NULL,
        [Symbol] [nvarchar](10) NULL,
        [IsActive] [bit] NOT NULL DEFAULT 1,
        [CreatedDate] [datetime] NOT NULL DEFAULT GETDATE(),
        [CreatedBy] [int] NULL,
        [ModifiedDate] [datetime] NULL,
        [ModifiedBy] [int] NULL,
        CONSTRAINT [PK_Currencies] PRIMARY KEY CLUSTERED ([CurrencyId] ASC),
        CONSTRAINT [UK_Currencies_Code] UNIQUE ([CurrencyCode])
    );
    PRINT N'تم إنشاء جدول Currencies';
END

-- جدول الدليل المحاسبي
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ChartOfAccounts]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[ChartOfAccounts](
        [AccountId] [int] IDENTITY(1,1) NOT NULL,
        [AccountCode] [nvarchar](20) NOT NULL,
        [AccountName] [nvarchar](200) NOT NULL,
        [AccountNameEn] [nvarchar](200) NULL,
        [ParentAccountId] [int] NULL,
        [AccountTypeId] [int] NOT NULL,
        [Level] [int] NOT NULL DEFAULT 1,
        [IsParent] [bit] NOT NULL DEFAULT 0,
        [IsActive] [bit] NOT NULL DEFAULT 1,
        [CurrencyId] [int] NULL,
        [Description] [nvarchar](500) NULL,
        [CreatedDate] [datetime] NOT NULL DEFAULT GETDATE(),
        [CreatedBy] [int] NULL,
        [ModifiedDate] [datetime] NULL,
        [ModifiedBy] [int] NULL,
        CONSTRAINT [PK_ChartOfAccounts] PRIMARY KEY CLUSTERED ([AccountId] ASC),
        CONSTRAINT [UK_ChartOfAccounts_Code] UNIQUE ([AccountCode]),
        CONSTRAINT [FK_ChartOfAccounts_Parent] FOREIGN KEY ([ParentAccountId]) REFERENCES [ChartOfAccounts]([AccountId]),
        CONSTRAINT [FK_ChartOfAccounts_Type] FOREIGN KEY ([AccountTypeId]) REFERENCES [AccountTypes]([AccountTypeId]),
        CONSTRAINT [FK_ChartOfAccounts_Currency] FOREIGN KEY ([CurrencyId]) REFERENCES [Currencies]([CurrencyId])
    );
    PRINT N'تم إنشاء جدول ChartOfAccounts';
END

-- إدراج البيانات الأساسية

-- إدراج المستخدم الافتراضي
IF NOT EXISTS (SELECT * FROM Users WHERE Username = 'admin')
BEGIN
    INSERT INTO Users (Username, PasswordHash, FullName, Email, IsActive)
    VALUES ('admin', 'admin123', N'مدير النظام', '<EMAIL>', 1);
    PRINT N'تم إنشاء المستخدم الافتراضي admin';
END

-- إدراج أنواع الحسابات الأساسية
IF NOT EXISTS (SELECT * FROM AccountTypes)
BEGIN
    INSERT INTO AccountTypes (TypeName, TypeNameEn, Description) VALUES
    (N'الأصول', 'Assets', N'حسابات الأصول'),
    (N'الخصوم', 'Liabilities', N'حسابات الخصوم'),
    (N'حقوق الملكية', 'Equity', N'حسابات حقوق الملكية'),
    (N'الإيرادات', 'Revenue', N'حسابات الإيرادات'),
    (N'المصروفات', 'Expenses', N'حسابات المصروفات');
    PRINT N'تم إدراج أنواع الحسابات الأساسية';
END

-- إدراج العملات الأساسية
IF NOT EXISTS (SELECT * FROM Currencies)
BEGIN
    INSERT INTO Currencies (CurrencyCode, CurrencyName, CurrencyNameEn, Symbol) VALUES
    ('SAR', N'ريال سعودي', 'Saudi Riyal', N'ر.س'),
    ('USD', N'دولار أمريكي', 'US Dollar', '$'),
    ('EUR', N'يورو', 'Euro', '€');
    PRINT N'تم إدراج العملات الأساسية';
END

-- إدراج الصلاحيات الأساسية
IF NOT EXISTS (SELECT * FROM Permissions WHERE PermissionName = 'ChartOfAccountsManagement')
BEGIN
    INSERT INTO Permissions (PermissionName, Description, Category) VALUES
    ('ChartOfAccountsManagement', N'إدارة الدليل المحاسبي', N'المحاسبة'),
    ('UserManagement', N'إدارة المستخدمين', N'الأمان'),
    ('RoleManagement', N'إدارة الأدوار', N'الأمان'),
    ('SystemSettings', N'إعدادات النظام', N'النظام');
    PRINT N'تم إدراج الصلاحيات الأساسية';
END

PRINT N'';
PRINT N'=== تم الانتهاء من الإعداد السريع لقاعدة البيانات ===';
PRINT N'يمكنك الآن استخدام النظام بالمستخدم: admin وكلمة المرور: admin123';
