using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Awqaf_Managment.DataAccess.Accounting;
using Awqaf_Managment.Models.Accounting;
using Awqaf_Managment.Models.Security;

namespace Awqaf_Managment.Services.Accounting
{
    /// <summary>
    /// خدمة إدارة الحسابات المحاسبية
    /// </summary>
    public static class AccountService
    {
        /// <summary>
        /// الحصول على جميع الحسابات
        /// </summary>
        public static List<Account> GetAllAccounts()
        {
            try
            {
                return AccountDataAccess.GetAllAccounts();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع الحسابات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// البحث في الحسابات
        /// </summary>
        public static List<Account> SearchAccounts(string searchTerm = null, int? accountTypeId = null, 
            int? parentAccountId = null, bool? isActive = null, bool? allowTransactions = null)
        {
            try
            {
                return AccountDataAccess.SearchAccounts(searchTerm, accountTypeId, parentAccountId, isActive, allowTransactions);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث في الحسابات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على الحسابات الهرمية
        /// </summary>
        public static List<Account> GetAccountsHierarchy(int? parentAccountId = null)
        {
            try
            {
                return AccountDataAccess.GetAccountsHierarchy(parentAccountId);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع الحسابات الهرمية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على حساب بالمعرف
        /// </summary>
        public static Account GetAccountById(int accountId)
        {
            try
            {
                if (accountId <= 0)
                    throw new ArgumentException("معرف الحساب غير صحيح");

                return AccountDataAccess.GetAccountById(accountId);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع الحساب: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إضافة حساب جديد
        /// </summary>
        public static int CreateAccount(Account account, int createdBy)
        {
            try
            {
                // التحقق من صحة البيانات
                var validationResult = ValidateAccount(account);
                if (!validationResult.IsValid)
                    throw new ArgumentException(validationResult.ErrorMessage);

                // التحقق من عدم تكرار رمز الحساب
                if (AccountDataAccess.IsAccountCodeExists(account.AccountCode))
                    throw new ArgumentException($"رمز الحساب '{account.AccountCode}' موجود مسبقاً");

                // التحقق من صحة الحساب الأب
                if (account.ParentAccountId.HasValue)
                {
                    var parentAccount = AccountDataAccess.GetAccountById(account.ParentAccountId.Value);
                    if (parentAccount == null)
                        throw new ArgumentException("الحساب الأب غير موجود");
                    
                    if (!parentAccount.IsActive)
                        throw new ArgumentException("لا يمكن إضافة حساب فرعي تحت حساب غير نشط");
                }

                return AccountDataAccess.InsertAccount(account, createdBy);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء الحساب: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث حساب
        /// </summary>
        public static void UpdateAccount(Account account, int modifiedBy)
        {
            try
            {
                // التحقق من وجود الحساب
                var existingAccount = AccountDataAccess.GetAccountById(account.AccountId);
                if (existingAccount == null)
                    throw new ArgumentException("الحساب غير موجود");

                // التحقق من صحة البيانات
                var validationResult = ValidateAccount(account);
                if (!validationResult.IsValid)
                    throw new ArgumentException(validationResult.ErrorMessage);

                // التحقق من عدم تكرار رمز الحساب
                if (AccountDataAccess.IsAccountCodeExists(account.AccountCode, account.AccountId))
                    throw new ArgumentException($"رمز الحساب '{account.AccountCode}' موجود مسبقاً");

                // التحقق من صحة الحساب الأب
                if (account.ParentAccountId.HasValue)
                {
                    if (account.ParentAccountId == account.AccountId)
                        throw new ArgumentException("لا يمكن أن يكون الحساب أباً لنفسه");

                    var parentAccount = AccountDataAccess.GetAccountById(account.ParentAccountId.Value);
                    if (parentAccount == null)
                        throw new ArgumentException("الحساب الأب غير موجود");
                    
                    if (!parentAccount.IsActive)
                        throw new ArgumentException("لا يمكن نقل الحساب تحت حساب غير نشط");

                    // التحقق من عدم إنشاء دورة في التسلسل الهرمي
                    if (IsCircularReference(account.AccountId, account.ParentAccountId.Value))
                        throw new ArgumentException("لا يمكن إنشاء مرجع دائري في التسلسل الهرمي");
                }

                AccountDataAccess.UpdateAccount(account, modifiedBy);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث الحساب: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حذف حساب
        /// </summary>
        public static void DeleteAccount(int accountId, int deletedBy)
        {
            try
            {
                // التحقق من وجود الحساب
                var account = AccountDataAccess.GetAccountById(accountId);
                if (account == null)
                    throw new ArgumentException("الحساب غير موجود");

                // التحقق من إمكانية الحذف
                if (!account.CanDelete())
                    throw new ArgumentException("لا يمكن حذف الحساب لوجود حسابات فرعية أو رصيد غير صفر");

                AccountDataAccess.DeleteAccount(accountId, deletedBy);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف الحساب: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// التحقق من صحة بيانات الحساب
        /// </summary>
        public static ValidationResult ValidateAccount(Account account)
        {
            if (account == null)
                return new ValidationResult(false, "بيانات الحساب مطلوبة");

            // التحقق من رمز الحساب
            if (string.IsNullOrWhiteSpace(account.AccountCode))
                return new ValidationResult(false, "رمز الحساب مطلوب");

            if (account.AccountCode.Length > 20)
                return new ValidationResult(false, "رمز الحساب يجب ألا يزيد عن 20 حرف");

            if (!IsValidAccountCode(account.AccountCode))
                return new ValidationResult(false, "رمز الحساب يجب أن يحتوي على أرقام وحروف إنجليزية فقط");

            // التحقق من اسم الحساب
            if (string.IsNullOrWhiteSpace(account.AccountNameAr))
                return new ValidationResult(false, "اسم الحساب باللغة العربية مطلوب");

            if (account.AccountNameAr.Length > 200)
                return new ValidationResult(false, "اسم الحساب يجب ألا يزيد عن 200 حرف");

            // التحقق من اسم الحساب بالإنجليزية
            if (!string.IsNullOrWhiteSpace(account.AccountNameEn) && account.AccountNameEn.Length > 200)
                return new ValidationResult(false, "اسم الحساب بالإنجليزية يجب ألا يزيد عن 200 حرف");

            // التحقق من نوع الحساب
            if (account.AccountTypeId <= 0)
                return new ValidationResult(false, "نوع الحساب مطلوب");

            // التحقق من العملة
            if (account.CurrencyId <= 0)
                return new ValidationResult(false, "العملة مطلوبة");

            // التحقق من الوصف
            if (!string.IsNullOrWhiteSpace(account.Description) && account.Description.Length > 500)
                return new ValidationResult(false, "الوصف يجب ألا يزيد عن 500 حرف");

            return new ValidationResult(true, "البيانات صحيحة");
        }

        /// <summary>
        /// التحقق من صحة رمز الحساب
        /// </summary>
        private static bool IsValidAccountCode(string accountCode)
        {
            // يجب أن يحتوي على أرقام وحروف إنجليزية فقط (مع السماح بالشرطة والنقطة)
            return Regex.IsMatch(accountCode, @"^[a-zA-Z0-9\-\.]+$");
        }

        /// <summary>
        /// التحقق من وجود مرجع دائري في التسلسل الهرمي
        /// </summary>
        private static bool IsCircularReference(int accountId, int parentAccountId)
        {
            var visited = new HashSet<int>();
            var currentId = parentAccountId;

            while (currentId > 0)
            {
                if (visited.Contains(currentId) || currentId == accountId)
                    return true;

                visited.Add(currentId);
                
                var parentAccount = AccountDataAccess.GetAccountById(currentId);
                if (parentAccount?.ParentAccountId == null)
                    break;

                currentId = parentAccount.ParentAccountId.Value;
            }

            return false;
        }

        /// <summary>
        /// الحصول على الحسابات الرئيسية
        /// </summary>
        public static List<Account> GetMainAccounts()
        {
            try
            {
                return AccountDataAccess.SearchAccounts(parentAccountId: null);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع الحسابات الرئيسية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على الحسابات الفرعية
        /// </summary>
        public static List<Account> GetChildAccounts(int parentAccountId)
        {
            try
            {
                return AccountDataAccess.SearchAccounts(parentAccountId: parentAccountId);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع الحسابات الفرعية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على أنواع الحسابات
        /// </summary>
        public static List<AccountType> GetAccountTypes()
        {
            try
            {
                return AccountTypeDataAccess.GetAllAccountTypes();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع أنواع الحسابات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على العملات
        /// </summary>
        public static List<Currency> GetCurrencies()
        {
            try
            {
                return CurrencyDataAccess.GetAllCurrencies();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع العملات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على العملة الأساسية
        /// </summary>
        public static Currency GetBaseCurrency()
        {
            try
            {
                return CurrencyDataAccess.GetBaseCurrency();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع العملة الأساسية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على البيانات الشخصية للحساب
        /// </summary>
        public static AccountPersonalInfo GetAccountPersonalInfo(int accountId)
        {
            try
            {
                return AccountPersonalInfoDataAccess.GetAccountPersonalInfo(accountId);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع البيانات الشخصية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حفظ البيانات الشخصية للحساب
        /// </summary>
        public static void SaveAccountPersonalInfo(AccountPersonalInfo personalInfo, int userId)
        {
            try
            {
                // التحقق من صحة البيانات
                var validationResult = ValidatePersonalInfo(personalInfo);
                if (!validationResult.IsValid)
                    throw new ArgumentException(validationResult.ErrorMessage);

                AccountPersonalInfoDataAccess.SaveAccountPersonalInfo(personalInfo, userId);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ البيانات الشخصية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات الشخصية
        /// </summary>
        public static ValidationResult ValidatePersonalInfo(AccountPersonalInfo personalInfo)
        {
            if (personalInfo == null)
                return new ValidationResult(false, "البيانات الشخصية مطلوبة");

            if (personalInfo.AccountId <= 0)
                return new ValidationResult(false, "معرف الحساب مطلوب");

            // التحقق من البريد الإلكتروني
            if (!string.IsNullOrWhiteSpace(personalInfo.Email) && !personalInfo.IsEmailValid())
                return new ValidationResult(false, "البريد الإلكتروني غير صحيح");

            // التحقق من رقم الهاتف
            if (!string.IsNullOrWhiteSpace(personalInfo.Phone) && !personalInfo.IsPhoneValid())
                return new ValidationResult(false, "رقم الهاتف غير صحيح");

            // التحقق من رقم الجوال
            if (!string.IsNullOrWhiteSpace(personalInfo.Mobile) && !personalInfo.IsMobileValid())
                return new ValidationResult(false, "رقم الجوال غير صحيح");

            return new ValidationResult(true, "البيانات صحيحة");
        }

        /// <summary>
        /// الحصول على سجل التعديلات للحساب
        /// </summary>
        public static List<AccountAuditLog> GetAccountAuditLog(int accountId, int pageSize = 50, int pageNumber = 1)
        {
            try
            {
                return AccountAuditLogDataAccess.GetAccountAuditLog(accountId, pageSize, pageNumber);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع سجل التعديلات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على جميع سجلات التعديلات
        /// </summary>
        public static List<AccountAuditLog> GetAllAuditLogs(DateTime? fromDate = null, DateTime? toDate = null,
            string action = null, int? userId = null, int pageSize = 100, int pageNumber = 1)
        {
            try
            {
                return AccountAuditLogDataAccess.GetAllAuditLogs(fromDate, toDate, action, userId, pageSize, pageNumber);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع سجلات التعديلات: {ex.Message}", ex);
            }
        }
    }

    /// <summary>
    /// نتيجة التحقق من صحة البيانات
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }

        public ValidationResult(bool isValid, string errorMessage = null)
        {
            IsValid = isValid;
            ErrorMessage = errorMessage;
        }
    }
}
