using System;
using System.Data;
using System.Data.SqlClient;
using Awqaf_Managment.Models.Accounting;

namespace Awqaf_Managment.DataAccess.Accounting
{
    /// <summary>
    /// طبقة الوصول لبيانات البيانات الشخصية للحسابات
    /// </summary>
    public static class AccountPersonalInfoDataAccess
    {
        /// <summary>
        /// الحصول على البيانات الشخصية للحساب
        /// </summary>
        public static AccountPersonalInfo GetAccountPersonalInfo(int accountId)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(@"
                        SELECT PersonalInfoId, AccountId, ContactPersonName, Email, Phone, Mobile,
                               Address, City, Country, PostalCode, IdentityNumber, TaxNumber, Notes,
                               CreatedDate, CreatedBy, ModifiedDate, ModifiedBy
                        FROM AccountPersonalInfo 
                        WHERE AccountId = @AccountId", connection))
                    {
                        command.Parameters.AddWithValue("@AccountId", accountId);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return MapReaderToAccountPersonalInfo(reader);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع البيانات الشخصية للحساب: {ex.Message}", ex);
            }

            return null;
        }

        /// <summary>
        /// إضافة أو تحديث البيانات الشخصية للحساب
        /// </summary>
        public static void SaveAccountPersonalInfo(AccountPersonalInfo personalInfo, int userId)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    
                    // التحقق من وجود البيانات الشخصية
                    var existingInfo = GetAccountPersonalInfo(personalInfo.AccountId);
                    
                    if (existingInfo == null)
                    {
                        // إضافة جديدة
                        using (var command = new SqlCommand(@"
                            INSERT INTO AccountPersonalInfo (
                                AccountId, ContactPersonName, Email, Phone, Mobile, Address, City, Country,
                                PostalCode, IdentityNumber, TaxNumber, Notes, CreatedBy
                            ) VALUES (
                                @AccountId, @ContactPersonName, @Email, @Phone, @Mobile, @Address, @City, @Country,
                                @PostalCode, @IdentityNumber, @TaxNumber, @Notes, @CreatedBy
                            )", connection))
                        {
                            AddPersonalInfoParameters(command, personalInfo);
                            command.Parameters.AddWithValue("@CreatedBy", userId);
                            command.ExecuteNonQuery();
                        }
                    }
                    else
                    {
                        // تحديث موجود
                        using (var command = new SqlCommand(@"
                            UPDATE AccountPersonalInfo SET
                                ContactPersonName = @ContactPersonName,
                                Email = @Email,
                                Phone = @Phone,
                                Mobile = @Mobile,
                                Address = @Address,
                                City = @City,
                                Country = @Country,
                                PostalCode = @PostalCode,
                                IdentityNumber = @IdentityNumber,
                                TaxNumber = @TaxNumber,
                                Notes = @Notes,
                                ModifiedDate = GETDATE(),
                                ModifiedBy = @ModifiedBy
                            WHERE AccountId = @AccountId", connection))
                        {
                            AddPersonalInfoParameters(command, personalInfo);
                            command.Parameters.AddWithValue("@ModifiedBy", userId);
                            command.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ البيانات الشخصية للحساب: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حذف البيانات الشخصية للحساب
        /// </summary>
        public static void DeleteAccountPersonalInfo(int accountId)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(@"
                        DELETE FROM AccountPersonalInfo WHERE AccountId = @AccountId", connection))
                    {
                        command.Parameters.AddWithValue("@AccountId", accountId);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف البيانات الشخصية للحساب: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إضافة معاملات البيانات الشخصية للأمر
        /// </summary>
        private static void AddPersonalInfoParameters(SqlCommand command, AccountPersonalInfo personalInfo)
        {
            command.Parameters.AddWithValue("@AccountId", personalInfo.AccountId);
            command.Parameters.AddWithValue("@ContactPersonName", (object)personalInfo.ContactPersonName ?? DBNull.Value);
            command.Parameters.AddWithValue("@Email", (object)personalInfo.Email ?? DBNull.Value);
            command.Parameters.AddWithValue("@Phone", (object)personalInfo.Phone ?? DBNull.Value);
            command.Parameters.AddWithValue("@Mobile", (object)personalInfo.Mobile ?? DBNull.Value);
            command.Parameters.AddWithValue("@Address", (object)personalInfo.Address ?? DBNull.Value);
            command.Parameters.AddWithValue("@City", (object)personalInfo.City ?? DBNull.Value);
            command.Parameters.AddWithValue("@Country", (object)personalInfo.Country ?? DBNull.Value);
            command.Parameters.AddWithValue("@PostalCode", (object)personalInfo.PostalCode ?? DBNull.Value);
            command.Parameters.AddWithValue("@IdentityNumber", (object)personalInfo.IdentityNumber ?? DBNull.Value);
            command.Parameters.AddWithValue("@TaxNumber", (object)personalInfo.TaxNumber ?? DBNull.Value);
            command.Parameters.AddWithValue("@Notes", (object)personalInfo.Notes ?? DBNull.Value);
        }

        /// <summary>
        /// تحويل قارئ البيانات إلى كائن البيانات الشخصية
        /// </summary>
        private static AccountPersonalInfo MapReaderToAccountPersonalInfo(SqlDataReader reader)
        {
            return new AccountPersonalInfo
            {
                PersonalInfoId = (int)reader["PersonalInfoId"],
                AccountId = (int)reader["AccountId"],
                ContactPersonName = reader["ContactPersonName"] != DBNull.Value ? reader["ContactPersonName"].ToString() : null,
                Email = reader["Email"] != DBNull.Value ? reader["Email"].ToString() : null,
                Phone = reader["Phone"] != DBNull.Value ? reader["Phone"].ToString() : null,
                Mobile = reader["Mobile"] != DBNull.Value ? reader["Mobile"].ToString() : null,
                Address = reader["Address"] != DBNull.Value ? reader["Address"].ToString() : null,
                City = reader["City"] != DBNull.Value ? reader["City"].ToString() : null,
                Country = reader["Country"] != DBNull.Value ? reader["Country"].ToString() : null,
                PostalCode = reader["PostalCode"] != DBNull.Value ? reader["PostalCode"].ToString() : null,
                IdentityNumber = reader["IdentityNumber"] != DBNull.Value ? reader["IdentityNumber"].ToString() : null,
                TaxNumber = reader["TaxNumber"] != DBNull.Value ? reader["TaxNumber"].ToString() : null,
                Notes = reader["Notes"] != DBNull.Value ? reader["Notes"].ToString() : null,
                CreatedDate = (DateTime)reader["CreatedDate"],
                CreatedBy = reader["CreatedBy"] != DBNull.Value ? (int?)reader["CreatedBy"] : null,
                ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? (DateTime?)reader["ModifiedDate"] : null,
                ModifiedBy = reader["ModifiedBy"] != DBNull.Value ? (int?)reader["ModifiedBy"] : null
            };
        }
    }
}
