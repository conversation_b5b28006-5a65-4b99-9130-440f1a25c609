using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using Awqaf_Managment.Models.Accounting;

namespace Awqaf_Managment.DataAccess.Accounting
{
    /// <summary>
    /// طبقة الوصول لبيانات العملات
    /// </summary>
    public static class CurrencyDataAccess
    {
        /// <summary>
        /// الحصول على جميع العملات
        /// </summary>
        public static List<Currency> GetAllCurrencies()
        {
            var currencies = new List<Currency>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(@"
                        SELECT CurrencyId, CurrencyCode, CurrencyNameEn, CurrencyNameAr, Symbol,
                               ExchangeRate, IsBaseCurrency, IsActive, CreatedDate, ModifiedDate
                        FROM Currencies
                        WHERE IsActive = 1
                        ORDER BY IsBaseCurrency DESC, CurrencyNameAr", connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                currencies.Add(new Currency
                                {
                                    CurrencyId = (int)reader["CurrencyId"],
                                    CurrencyCode = reader["CurrencyCode"].ToString(),
                                    CurrencyNameEn = reader["CurrencyNameEn"] != DBNull.Value ? reader["CurrencyNameEn"].ToString() : "",
                                    CurrencyNameAr = reader["CurrencyNameAr"].ToString(),
                                    Symbol = reader["Symbol"] != DBNull.Value ? reader["Symbol"].ToString() : "",
                                    ExchangeRate = (decimal)reader["ExchangeRate"],
                                    IsBaseCurrency = (bool)reader["IsBaseCurrency"],
                                    IsActive = (bool)reader["IsActive"],
                                    CreatedDate = (DateTime)reader["CreatedDate"],
                                    ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? (DateTime?)reader["ModifiedDate"] : null
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع العملات: {ex.Message}", ex);
            }

            return currencies;
        }

        /// <summary>
        /// الحصول على عملة بالمعرف
        /// </summary>
        public static Currency GetCurrencyById(int currencyId)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(@"
                        SELECT CurrencyId, CurrencyCode, CurrencyNameEn, CurrencyNameAr, Symbol,
                               ExchangeRate, IsBaseCurrency, IsActive, CreatedDate, ModifiedDate
                        FROM Currencies
                        WHERE CurrencyId = @CurrencyId", connection))
                    {
                        command.Parameters.AddWithValue("@CurrencyId", currencyId);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new Currency
                                {
                                    CurrencyId = (int)reader["CurrencyId"],
                                    CurrencyCode = reader["CurrencyCode"].ToString(),
                                    CurrencyNameEn = reader["CurrencyName"].ToString(),
                                    CurrencyNameAr = reader["CurrencyNameAr"].ToString(),
                                    Symbol = reader["Symbol"] != DBNull.Value ? reader["Symbol"].ToString() : "",
                                    ExchangeRate = (decimal)reader["ExchangeRate"],
                                    IsBaseCurrency = (bool)reader["IsBaseCurrency"],
                                    IsActive = (bool)reader["IsActive"],
                                    CreatedDate = (DateTime)reader["CreatedDate"],
                                    ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? (DateTime?)reader["ModifiedDate"] : null
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع العملة: {ex.Message}", ex);
            }

            return null;
        }

        /// <summary>
        /// الحصول على العملة الأساسية
        /// </summary>
        public static Currency GetBaseCurrency()
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(@"
                        SELECT CurrencyId, CurrencyCode, CurrencyNameEn, CurrencyNameAr, Symbol,
                               ExchangeRate, IsBaseCurrency, IsActive, CreatedDate, ModifiedDate
                        FROM Currencies
                        WHERE IsBaseCurrency = 1 AND IsActive = 1", connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new Currency
                                {
                                    CurrencyId = (int)reader["CurrencyId"],
                                    CurrencyCode = reader["CurrencyCode"].ToString(),
                                    CurrencyNameEn = reader["CurrencyNameEn"].ToString(),
                                    CurrencyNameAr = reader["CurrencyNameAr"].ToString(),
                                    Symbol = reader["Symbol"] != DBNull.Value ? reader["Symbol"].ToString() : "",
                                    ExchangeRate = (decimal)reader["ExchangeRate"],
                                    IsBaseCurrency = (bool)reader["IsBaseCurrency"],
                                    IsActive = (bool)reader["IsActive"],
                                    CreatedDate = (DateTime)reader["CreatedDate"],
                                    ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? (DateTime?)reader["ModifiedDate"] : null
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع العملة الأساسية: {ex.Message}", ex);
            }

            return null;
        }

        /// <summary>
        /// البحث في العملات
        /// </summary>
        public static List<Currency> SearchCurrencies(string searchTerm)
        {
            var currencies = new List<Currency>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(@"
                        SELECT CurrencyId, CurrencyCode, CurrencyNameEn, CurrencyNameAr, Symbol,
                               ExchangeRate, IsBaseCurrency, IsActive, CreatedDate, ModifiedDate
                        FROM Currencies
                        WHERE IsActive = 1
                        AND (CurrencyCode LIKE @SearchTerm OR CurrencyName LIKE @SearchTerm OR CurrencyNameAr LIKE @SearchTerm)
                        ORDER BY IsBaseCurrency DESC, CurrencyNameAr", connection))
                    {
                        command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");
                        
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                currencies.Add(new Currency
                                {
                                    CurrencyId = (int)reader["CurrencyId"],
                                    CurrencyCode = reader["CurrencyCode"].ToString(),
                                    CurrencyNameEn = reader["CurrencyNameEn"].ToString(),
                                    CurrencyNameAr = reader["CurrencyNameAr"].ToString(),
                                    Symbol = reader["Symbol"] != DBNull.Value ? reader["Symbol"].ToString() : "",
                                    ExchangeRate = (decimal)reader["ExchangeRate"],
                                    IsBaseCurrency = (bool)reader["IsBaseCurrency"],
                                    IsActive = (bool)reader["IsActive"],
                                    CreatedDate = (DateTime)reader["CreatedDate"],
                                    ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? (DateTime?)reader["ModifiedDate"] : null
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث في العملات: {ex.Message}", ex);
            }

            return currencies;
        }
    }
}
