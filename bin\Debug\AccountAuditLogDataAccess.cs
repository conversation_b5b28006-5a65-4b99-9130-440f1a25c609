using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using Awqaf_Managment.Models.Accounting;

namespace Awqaf_Managment.DataAccess.Accounting
{
    /// <summary>
    /// طبقة الوصول لبيانات سجل تعديلات الحسابات
    /// </summary>
    public static class AccountAuditLogDataAccess
    {
        /// <summary>
        /// الحصول على سجل التعديلات للحساب
        /// </summary>
        public static List<AccountAuditLog> GetAccountAuditLog(int accountId, int pageSize = 50, int pageNumber = 1)
        {
            var auditLogs = new List<AccountAuditLog>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(@"
                        SELECT AuditId, AccountId, Action, FieldName, OldValue, NewValue,
                               ActionDate, UserId, UserName, IPAddress, Description
                        FROM AccountAuditLog 
                        WHERE AccountId = @AccountId
                        ORDER BY ActionDate DESC
                        OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY", connection))
                    {
                        command.Parameters.AddWithValue("@AccountId", accountId);
                        command.Parameters.AddWithValue("@Offset", (pageNumber - 1) * pageSize);
                        command.Parameters.AddWithValue("@PageSize", pageSize);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                auditLogs.Add(MapReaderToAuditLog(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع سجل التعديلات: {ex.Message}", ex);
            }

            return auditLogs;
        }

        /// <summary>
        /// الحصول على جميع سجلات التعديلات
        /// </summary>
        public static List<AccountAuditLog> GetAllAuditLogs(DateTime? fromDate = null, DateTime? toDate = null, 
            string action = null, int? userId = null, int pageSize = 100, int pageNumber = 1)
        {
            var auditLogs = new List<AccountAuditLog>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    
                    var sql = @"
                        SELECT al.AuditId, al.AccountId, al.Action, al.FieldName, al.OldValue, al.NewValue,
                               al.ActionDate, al.UserId, al.UserName, al.IPAddress, al.Description,
                               a.AccountCode, a.AccountNameAr
                        FROM AccountAuditLog al
                        LEFT JOIN ChartOfAccounts a ON al.AccountId = a.AccountId
                        WHERE 1=1";
                    
                    if (fromDate.HasValue)
                        sql += " AND al.ActionDate >= @FromDate";
                    if (toDate.HasValue)
                        sql += " AND al.ActionDate <= @ToDate";
                    if (!string.IsNullOrWhiteSpace(action))
                        sql += " AND al.Action = @Action";
                    if (userId.HasValue)
                        sql += " AND al.UserId = @UserId";
                    
                    sql += @" ORDER BY al.ActionDate DESC
                             OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        if (fromDate.HasValue)
                            command.Parameters.AddWithValue("@FromDate", fromDate.Value);
                        if (toDate.HasValue)
                            command.Parameters.AddWithValue("@ToDate", toDate.Value);
                        if (!string.IsNullOrWhiteSpace(action))
                            command.Parameters.AddWithValue("@Action", action);
                        if (userId.HasValue)
                            command.Parameters.AddWithValue("@UserId", userId.Value);
                        
                        command.Parameters.AddWithValue("@Offset", (pageNumber - 1) * pageSize);
                        command.Parameters.AddWithValue("@PageSize", pageSize);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var auditLog = MapReaderToAuditLog(reader);
                                
                                // إضافة معلومات الحساب إذا كانت متوفرة
                                if (reader["AccountCode"] != DBNull.Value && reader["AccountNameAr"] != DBNull.Value)
                                {
                                    auditLog.Account = new Account
                                    {
                                        AccountId = auditLog.AccountId,
                                        AccountCode = reader["AccountCode"].ToString(),
                                        AccountNameAr = reader["AccountNameAr"].ToString()
                                    };
                                }
                                
                                auditLogs.Add(auditLog);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع سجلات التعديلات: {ex.Message}", ex);
            }

            return auditLogs;
        }

        /// <summary>
        /// إضافة سجل تعديل جديد
        /// </summary>
        public static void InsertAuditLog(AccountAuditLog auditLog)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(@"
                        INSERT INTO AccountAuditLog (
                            AccountId, Action, FieldName, OldValue, NewValue, 
                            UserId, UserName, IPAddress, Description
                        ) VALUES (
                            @AccountId, @Action, @FieldName, @OldValue, @NewValue,
                            @UserId, @UserName, @IPAddress, @Description
                        )", connection))
                    {
                        command.Parameters.AddWithValue("@AccountId", auditLog.AccountId);
                        command.Parameters.AddWithValue("@Action", auditLog.Action);
                        command.Parameters.AddWithValue("@FieldName", (object)auditLog.FieldName ?? DBNull.Value);
                        command.Parameters.AddWithValue("@OldValue", (object)auditLog.OldValue ?? DBNull.Value);
                        command.Parameters.AddWithValue("@NewValue", (object)auditLog.NewValue ?? DBNull.Value);
                        command.Parameters.AddWithValue("@UserId", auditLog.UserId);
                        command.Parameters.AddWithValue("@UserName", auditLog.UserName);
                        command.Parameters.AddWithValue("@IPAddress", (object)auditLog.IPAddress ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Description", (object)auditLog.Description ?? DBNull.Value);
                        
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة سجل التعديل: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على عدد سجلات التعديلات للحساب
        /// </summary>
        public static int GetAuditLogCount(int accountId)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(@"
                        SELECT COUNT(*) FROM AccountAuditLog WHERE AccountId = @AccountId", connection))
                    {
                        command.Parameters.AddWithValue("@AccountId", accountId);
                        return (int)command.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حساب عدد سجلات التعديلات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على إحصائيات سجل التعديلات
        /// </summary>
        public static Dictionary<string, int> GetAuditLogStatistics(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var statistics = new Dictionary<string, int>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    
                    var sql = @"
                        SELECT Action, COUNT(*) as Count
                        FROM AccountAuditLog 
                        WHERE 1=1";
                    
                    if (fromDate.HasValue)
                        sql += " AND ActionDate >= @FromDate";
                    if (toDate.HasValue)
                        sql += " AND ActionDate <= @ToDate";
                    
                    sql += " GROUP BY Action";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        if (fromDate.HasValue)
                            command.Parameters.AddWithValue("@FromDate", fromDate.Value);
                        if (toDate.HasValue)
                            command.Parameters.AddWithValue("@ToDate", toDate.Value);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                statistics[reader["Action"].ToString()] = (int)reader["Count"];
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع إحصائيات سجل التعديلات: {ex.Message}", ex);
            }

            return statistics;
        }

        /// <summary>
        /// تحويل قارئ البيانات إلى كائن سجل التعديل
        /// </summary>
        private static AccountAuditLog MapReaderToAuditLog(SqlDataReader reader)
        {
            return new AccountAuditLog
            {
                AuditId = (int)reader["AuditId"],
                AccountId = (int)reader["AccountId"],
                Action = reader["Action"].ToString(),
                FieldName = reader["FieldName"] != DBNull.Value ? reader["FieldName"].ToString() : null,
                OldValue = reader["OldValue"] != DBNull.Value ? reader["OldValue"].ToString() : null,
                NewValue = reader["NewValue"] != DBNull.Value ? reader["NewValue"].ToString() : null,
                ActionDate = (DateTime)reader["ActionDate"],
                UserId = (int)reader["UserId"],
                UserName = reader["UserName"].ToString(),
                IPAddress = reader["IPAddress"] != DBNull.Value ? reader["IPAddress"].ToString() : null,
                Description = reader["Description"] != DBNull.Value ? reader["Description"].ToString() : null
            };
        }
    }
}
